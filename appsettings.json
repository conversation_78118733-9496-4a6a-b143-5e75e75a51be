{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "General": {"BaseDomain": "localhost:7173", "AdminId": "d1c2f36a-41a7-4a2d-a5e2-c79e95f1a216", "AdminEmail": "<EMAIL>", "AdminPassword": "Test@123", "PasswordSalt": "gla9JHbk8L0q118GyKEfxQ=="}, "ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5433;Database=vendingmachinelocal;Username=postgres;Password=root"}, "SmtpClient": {"Server": "smtp.gmail.com", "Port": 587, "User": "<EMAIL>", "Password": "wguihskuwvwlupph", "RequiresAuthentication": true, "SocketOptions": 1}, "Jwt": {"Key": "This is where you should specify your secret key, which is used to sign and verify Jwt tokens.", "Issuer": "vendormonitor.invimatic.com"}, "CORSSettings": {"Origins": "http://localhost:8000"}, "AllowedHosts": "*"}