using System.Collections;
using System.Text.Json.Nodes;
using VendorMonitor.Api.Common;
using VendorMonitor.Api.Infrastructure;
using VendorMonitor.Api.Services;
using VendorMonitor.Api.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System.Security.Claims;

namespace VendorMonitor.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class DashboardController(MachineService machineService) : ControllerBase
{

    [HttpGet("Machine")]
    [Authorize]
    public async Task<IActionResult> GetMachines(string quickSearch, string query, int page = 0, int size = 20)
    {
        var userId = HttpContext.User.Claims.FirstOrDefault(claim => claim.Type == ClaimTypes.NameIdentifier)?.Value;
        var role = HttpContext.User.Claims.FirstOrDefault(claim => claim.Type == ClaimTypes.Role)?.Value;
        MachineRequestViewModel queryModel = null;
        if (!string.IsNullOrEmpty(query))
        {
            queryModel = System.Text.Json.JsonSerializer.Deserialize<MachineRequestViewModel>(query);
        }
        var response = await machineService.GetMachines(page, size > 20 ? 20 : size, userId, role, queryModel,quickSearch);
        return new JsonResult(new
        {
            Status = 200,
            Result = new
            {
                Count = response.Item1,
                Items = response.Item2
            }
        });
    }

    // [HttpGet("Machine/Downlaod")]
    // [Authorize]
    // public async Task<IActionResult> GetMachines(string query,int page = 0, int size = 20)
    // {
    //       var userId = HttpContext.User.Claims.FirstOrDefault(claim => claim.Type == ClaimTypes.NameIdentifier)?.Value;
    //     var role = HttpContext.User.Claims.FirstOrDefault(claim => claim.Type == ClaimTypes.Role)?.Value;
    //     var queryModel = System.Text.Json.JsonSerializer.Deserialize<MachineRequestViewModel>(query);
    //     var response = await machineService.GetMachines(page, size > 20 ? 20 : size, userId, role,queryModel);
    //     return new JsonResult(new
    //     {
    //         Status = 200,
    //         Result = new
    //         {
    //             Count = response.Item1,
    //             Items = response.Item2
    //         }
    //     });
    // }

    [HttpGet("Transaction")]
    [Authorize]
    public async Task<IActionResult> GetTransactions(string query, int page = 0, int size = 20)
    {
        var userId = HttpContext.User.Claims.FirstOrDefault(claim => claim.Type == ClaimTypes.NameIdentifier)?.Value;
        var role = HttpContext.User.Claims.FirstOrDefault(claim => claim.Type == ClaimTypes.Role)?.Value;
           var transactionRequestViewModel = string.IsNullOrEmpty(query) || query == "{}"
        ? null
            : System.Text.Json.JsonSerializer.Deserialize<TransactionRequestViewModel>(query);

        var response = await machineService.GetTransactions(page, size > 20 ? 20 : size, userId, role, transactionRequestViewModel);

        return new JsonResult(new
        {
            Status = 200,
            Result = new
            {
                Count = response.Item1,
                Items = response.Item2
            }
        });
    }


    [HttpGet("Revenue")]
    [Authorize]
    public async Task<IActionResult> GetRevenue(string query)
    {

        var userId = HttpContext.User.Claims.FirstOrDefault(claim => claim.Type == ClaimTypes.NameIdentifier)?.Value;
        var role = HttpContext.User.Claims.FirstOrDefault(claim => claim.Type == ClaimTypes.Role)?.Value;
        var revenueRequest = string.IsNullOrEmpty(query)
            ? null
            : System.Text.Json.JsonSerializer.Deserialize<RevenueRequestViewModel>(query);


        var response = await machineService.GetRevenue(revenueRequest, userId, role);
        return new JsonResult(new
        {
            Status = 200,
            Result = new
            {
                Count = response.Item1,
                Items = response.Item2
            }
        });
    }

    [HttpGet("RevenueSummary")]
    [Authorize]
    public async Task<IActionResult> GetRevenueSummary(string query)
    {
        var userId = HttpContext.User.Claims.FirstOrDefault(claim => claim.Type == ClaimTypes.NameIdentifier)?.Value;
        var role = HttpContext.User.Claims.FirstOrDefault(claim => claim.Type == ClaimTypes.Role)?.Value;
        var revenueRequest = string.IsNullOrEmpty(query)
            ? null
            : System.Text.Json.JsonSerializer.Deserialize<RevenueSummaryRequestViewModel>(query);
        var revenueSummary = await machineService.GetRevenueSummary(revenueRequest, userId, role);
        return Ok(new
        {
            Status = 200,
            Result = revenueSummary
        });
    }
}
