using VendorMonitor.Api.Models;
using Microsoft.EntityFrameworkCore;
using OfficeOpenXml;
using System.Text.RegularExpressions;
using VendorMonitor.Api.Data;

namespace ExcelMigrationApi.Services;

public class ExcelService : IExcelService
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<ExcelService> _logger;
    private static readonly Dictionary<string, string[]> ColumnVariations = new Dictionary<string, string[]>
    {
        { "Account Email Address", new[] { "AccountEmail", "Account Email", "EmailAddress" } },
        { "Account First Name", new[] { "AccountFirstName", "First Name" } },
        { "Account Last Name", new[] { "AccountLastName", "Last Name" } },
        { "Account Phone Number", new[] { "AccountPhoneNumber", "Phone Number" } },
        { "Maintainer Number", new[] { "MaintainerNumber", "Maitainer Number", "MaitainerNumber" } },
        { "Machine ID", new[] { "MachineID", "MachineId", "Machine Id" } },
        { "Role", new[] { "UserRole", "AccountRole", "User Role", "Account Role" } },
        { "Salt", new[] { "UserSalt", "AccountSalt" } },
        { "Key", new[] { "UserKey", "AccountKey" } }
    };

    private static readonly string[] RequiredColumns = new[] {
        "Machine Name", "Machine Sim/Wi-Fi", "Machine Sim No.", "Price", "Stock Capacity",
        "Location", "Account Email Address", "Account First Name", "Account Last Name",
        "Account Phone Number", "Role", "Salt", "Key"
    };

    static ExcelService()
    {
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
    }

    public ExcelService(ApplicationDbContext context, ILogger<ExcelService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<(bool isValid, string message, List<Machine> data, List<FailedRecord> failedRecords)> ProcessExcelFile(IFormFile file)
    {
        var data = new List<Machine>();
        var failedRecords = new List<FailedRecord>();

        if (file == null || file.Length == 0)
            return (false, "File is empty", data, failedRecords);

        using (var stream = new MemoryStream())
        {
            await file.CopyToAsync(stream);
            using (var package = new ExcelPackage(stream))
            {
                var worksheet = package.Workbook.Worksheets.FirstOrDefault();
                if (worksheet == null)
                    return (false, "No worksheet found", data, failedRecords);

                // First validate if Account Email Address column exists
                var columnHeaders = new List<string>();
                for (int col = 1; col <= worksheet.Dimension.End.Column; col++)
                {
                    var header = worksheet.Cells[1, col].Text.Trim();
                    if (!string.IsNullOrWhiteSpace(header))
                    {
                        columnHeaders.Add(header);
                    }
                }

                _logger.LogInformation("Found column headers: " + string.Join(", ", columnHeaders));

                // Check for required Account Email Address column first
                if (!IsColumnPresent(columnHeaders, "Account Email Address"))
                {
                    return (false, "Required column 'Account Email Address' is missing", data, failedRecords);
                }

                // Then check other required columns
                var missingColumns = RequiredColumns
                    .Where(rc => !IsColumnPresent(columnHeaders, rc))
                    .ToList();

                if (missingColumns.Any())
                {
                    var errorMsg = $"Required columns are missing: {string.Join(", ", missingColumns)}. Found columns: {string.Join(", ", columnHeaders)}";
                    _logger.LogError(errorMsg);
                    return (false, errorMsg, data, failedRecords);
                }

                int accountEmailIndex = GetColumnIndex(columnHeaders, "Account Email Address");
                int accountFirstNameIndex = GetColumnIndex(columnHeaders, "Account First Name");
                int accountLastNameIndex = GetColumnIndex(columnHeaders, "Account Last Name");
                int accountPhoneNumberIndex = GetColumnIndex(columnHeaders, "Account Phone Number");
                int roleIndex = GetColumnIndex(columnHeaders, "Role");
                _logger.LogInformation("Role column index: {Index}", roleIndex);
                int saltIndex = GetColumnIndex(columnHeaders, "Salt");
                int keyIndex = GetColumnIndex(columnHeaders, "Key");

                // Collect all valid emails first
                var validEmails = new List<(int Row, string Email)>();

                for (int row = 2; row <= worksheet.Dimension.End.Row; row++)
                {
                    if (IsRowEmpty(worksheet, row))
                        continue;

                    var email = worksheet.Cells[row, accountEmailIndex].Text;
                    if (string.IsNullOrEmpty(email))
                    {
                        failedRecords.Add(new FailedRecord(row, email, "Email address cannot be empty"));
                        continue;
                    }

                    if (!IsValidEmail(email))
                    {
                        failedRecords.Add(new FailedRecord(row, email, "Invalid email format"));
                        continue;
                    }

                    validEmails.Add((row, email));
                }

                if (validEmails.Count == 0)
                {
                    return (false, "No valid email addresses found in the Excel file", data, failedRecords);
                }

                // Get existing users for valid emails
                var emailAddresses = validEmails.Select(e => e.Email).ToList();
                var existingUsers = await _context.Users
                    .Where(u => emailAddresses.Contains(u.EmailAddress) && !u.IsDeleted)
                    .ToDictionaryAsync(u => u.EmailAddress);

                // Track which rows have valid users
                var validRows = new List<(int Row, string Email)>();

                // Add failed records for non-existent users
                foreach (var (row, email) in validEmails)
                {
                    if (!existingUsers.ContainsKey(email))
                    {
                        _logger.LogWarning("User not found with email {Email} - skipping machine creation/update for row {Row}", email, row);
                        failedRecords.Add(new FailedRecord(row, email, "User not found with this email - machine will not be created or updated"));
                    }
                    else
                    {
                        _logger.LogInformation("Found existing user with email {Email} - will process machine creation/update for row {Row}", email, row);
                        validRows.Add((row, email));
                    }
                }

                if (validRows.Count == 0)
                {
                    return (false, "No valid users found in the database", data, failedRecords);
                }

                // Now proceed with processing the rest of the data
                // Get required column indices
                int machineNameIndex = GetColumnIndex(columnHeaders, "Machine Name");
                int machineSimWiFiIndex = GetColumnIndex(columnHeaders, "Machine Sim/Wi-Fi");
                int machineSimNoIndex = GetColumnIndex(columnHeaders, "Machine Sim No.");
                int priceIndex = GetColumnIndex(columnHeaders, "Price");
                int stockCapacityIndex = GetColumnIndex(columnHeaders, "Stock Capacity");
                int locationIndex = GetColumnIndex(columnHeaders, "Location");

                // Get optional column indices - these won't throw exceptions if not found
                int machineIdIndex = GetOptionalColumnIndex(columnHeaders, "Machine ID");
                int startDateIndex = GetOptionalColumnIndex(columnHeaders, "Start Date");
                int endDateIndex = GetOptionalColumnIndex(columnHeaders, "End Date");
                int resellerIndex = GetOptionalColumnIndex(columnHeaders, "Reseller");

                // Get a list of all Machine Sim Numbers from the Excel file first
                var machineSimNumbers = new HashSet<string>();
                var rowsBySimNumber = new Dictionary<string, (int Row, string Email)>();

                foreach (var (row, email) in validRows)
                {
                    var machineSimNumber = worksheet.Cells[row, machineSimNoIndex].Text?.Trim();
                    if (string.IsNullOrEmpty(machineSimNumber))
                    {
                        failedRecords.Add(new FailedRecord(row, email, "Machine Sim Number cannot be empty"));
                        continue;
                    }
                    // if (!machineSimNumbers.Add(machineSimNumber))
                    // {
                    //     failedRecords.Add(new FailedRecord(row, email, $"Duplicate Machine Sim Number '{machineSimNumber}' in Excel file"));
                    //     continue;
                    // }

                    // Store the row and email for this sim number
                    rowsBySimNumber[machineSimNumber] = (row, email);
                }

                // Get all existing machines with these Sim Numbers in one query
                // We'll look for machines where the MaintainerNumber matches the Machine Sim Number from Excel
                var existingMachines = await _context.Machines
                    .Where(m => machineSimNumbers.Contains(m.MaintainerNumber))
                    .ToDictionaryAsync(m => m.MaintainerNumber);

                // First extract MachineId from Machine Name for all rows
                var extractedMachineIds = new Dictionary<string, (int Row, string Email)>();

                foreach (var (row, email) in validRows)
                {
                    var machineName = worksheet.Cells[row, machineNameIndex].Text?.Trim();
                    if (!string.IsNullOrEmpty(machineName) && machineName.Contains('_'))
                    {
                        var extractedMachineId = machineName.Split('_').LastOrDefault()?.Trim();
                        if (!string.IsNullOrEmpty(extractedMachineId))
                        {
                            _logger.LogInformation("Extracted MachineId '{MachineId}' from Machine Name '{MachineName}'", 
                                extractedMachineId, machineName);
                            extractedMachineIds[extractedMachineId] = (row, email);
                        }
                    }
                }

                // Get all existing machines by extracted MachineIds in one query
                var machineIdsToFind = extractedMachineIds.Keys.ToList();
                var existingMachinesByMachineId = await _context.Machines
                    .Where(m => machineIdsToFind.Contains(m.MachineId))
                    .ToDictionaryAsync(m => m.MachineId);

                _logger.LogInformation("Found {Count} existing machines by extracted MachineId", 
                    existingMachinesByMachineId.Count);

                // Process only valid rows
                foreach (var (row, email) in validRows)
                {
                    try
                    {
                        // Log the current row being processed to help with debugging
                        _logger.LogInformation("Processing row {Row} for email {Email}", row, email);
                        
                        var machineName = worksheet.Cells[row, machineNameIndex].Text?.Trim();
                        var currentExtractedMachineId = string.Empty;
                        
                        // Extract MachineId from Machine Name
                        if (!string.IsNullOrEmpty(machineName) && machineName.Contains('_'))
                        {
                            extractedMachineId = machineName.Split('_').LastOrDefault()?.Trim();
                        }
                        
                        // Find machine by extracted MachineId
                        Machine machine = null;
                        if (!string.IsNullOrEmpty(extractedMachineId) && 
                            existingMachinesByMachineId.TryGetValue(extractedMachineId, out var existingMachine))
                        {
                            _logger.LogInformation("Found existing machine with MachineId {MachineId} extracted from Machine Name", 
                                extractedMachineId);
                            machine = existingMachine;
                            
                            // Update existing machine
                            _context.Machines.Attach(machine);
                            _context.Entry(machine).State = EntityState.Modified;
                        }
                        else
                        {
                            // Create new machine
                            _logger.LogInformation("Creating new machine with extracted MachineId {MachineId}", 
                                !string.IsNullOrEmpty(extractedMachineId) ? extractedMachineId : "0000");
                            
                            machine = new Machine
                            {
                                Id = Guid.NewGuid().ToString(),
                                CreatedTime = DateTime.UtcNow,
                                MachineId = !string.IsNullOrEmpty(extractedMachineId) ? extractedMachineId : "0000",
                                Name = !string.IsNullOrEmpty(machineName) ? machineName : "VEND0000",
                                MaintainerNumber = worksheet.Cells[row, machineSimNoIndex].Text?.Trim()
                            };
                            
                            _context.Machines.Add(machine);
                        }
                        
                        // Continue with updating machine properties...
                        var user = existingUsers[email]; // We know this exists
                        var machineSimNumber = worksheet.Cells[row, machineSimNoIndex].Text?.Trim();

                        // Skip if we already determined this machine sim number is invalid
                        if (string.IsNullOrEmpty(machineSimNumber) || !machineSimNumbers.Contains(machineSimNumber))
                            continue;

                        // Get user fields from Excel with validation
                        string firstName = null, lastName = null, phoneNumber = null, role = null, salt = null, key = null;

                        if (accountFirstNameIndex > 0 && accountFirstNameIndex <= worksheet.Dimension.End.Column)
                            firstName = worksheet.Cells[row, accountFirstNameIndex].Text;
                        else
                            _logger.LogWarning("Account First Name column index {Index} is out of range", accountFirstNameIndex);

                        if (accountLastNameIndex > 0 && accountLastNameIndex <= worksheet.Dimension.End.Column)
                            lastName = worksheet.Cells[row, accountLastNameIndex].Text;
                        else
                            _logger.LogWarning("Account Last Name column index {Index} is out of range", accountLastNameIndex);

                        if (accountPhoneNumberIndex > 0 && accountPhoneNumberIndex <= worksheet.Dimension.End.Column)
                            phoneNumber = worksheet.Cells[row, accountPhoneNumberIndex].Text;
                        else
                            _logger.LogWarning("Account Phone Number column index {Index} is out of range", accountPhoneNumberIndex);

                        if (roleIndex > 0 && roleIndex <= worksheet.Dimension.End.Column)
                        {
                            role = worksheet.Cells[row, roleIndex].Text?.Trim();
                            _logger.LogInformation("Read role '{Role}' from Excel for user {Email} at column index {Index}",
                                role, email, roleIndex);
                        }
                        else
                            _logger.LogWarning("Role column index {Index} is out of range", roleIndex);

                        if (saltIndex > 0 && saltIndex <= worksheet.Dimension.End.Column)
                            salt = worksheet.Cells[row, saltIndex].Text;
                        else
                            _logger.LogWarning("Salt column index {Index} is out of range", saltIndex);

                        if (keyIndex > 0 && keyIndex <= worksheet.Dimension.End.Column)
                            key = worksheet.Cells[row, keyIndex].Text;
                        else
                            _logger.LogWarning("Key column index {Index} is out of range", keyIndex);

                        // Always update user fields from Excel, even if they're the same as current values
                        // This ensures that any changes in the Excel file are applied to the database
                        bool userUpdated = false;

                        // Log the current user values before updating
                        _logger.LogInformation("Current user values - Email: {Email}, FirstName: '{FirstName}', LastName: '{LastName}', " +
                            "PhoneNumber: '{PhoneNumber}', Role: '{Role}'",
                            user.EmailAddress, user.FirstName, user.LastName, user.PhoneNumber, user.Role);

                        // Log the values from Excel
                        _logger.LogInformation("Excel values - FirstName: '{FirstName}', LastName: '{LastName}', " +
                            "PhoneNumber: '{PhoneNumber}', Role: '{Role}'",
                            firstName, lastName, phoneNumber, role);

                        // Update FirstName if provided in Excel
                        if (!string.IsNullOrWhiteSpace(firstName))
                        {
                            if (user.FirstName != firstName)
                            {
                                _logger.LogInformation("Updating user {Email} FirstName from '{OldValue}' to '{NewValue}'",
                                    user.EmailAddress, user.FirstName, firstName);
                            }
                            user.FirstName = firstName;
                            userUpdated = true;
                        }

                        // Update LastName if provided in Excel
                        if (!string.IsNullOrWhiteSpace(lastName))
                        {
                            if (user.LastName != lastName)
                            {
                                _logger.LogInformation("Updating user {Email} LastName from '{OldValue}' to '{NewValue}'",
                                    user.EmailAddress, user.LastName, lastName);
                            }
                            user.LastName = lastName;
                            userUpdated = true;
                        }

                        // Update PhoneNumber if provided in Excel
                        if (!string.IsNullOrWhiteSpace(phoneNumber))
                        {
                            if (user.PhoneNumber != phoneNumber)
                            {
                                _logger.LogInformation("Updating user {Email} PhoneNumber from '{OldValue}' to '{NewValue}'",
                                    user.EmailAddress, user.PhoneNumber, phoneNumber);
                            }
                            user.PhoneNumber = phoneNumber;
                            userUpdated = true;
                        }

                        // Update Role if provided in Excel
                        if (!string.IsNullOrWhiteSpace(role))
                        {
                            if (user.Role != role)
                            {
                                _logger.LogInformation("Updating user {Email} Role from '{OldValue}' to '{NewValue}'",
                                    user.EmailAddress, user.Role, role);
                            }
                            user.Role = role;
                            userUpdated = true;
                        }
                        else
                        {
                            _logger.LogWarning("Role value from Excel is empty or whitespace for user {Email}", user.EmailAddress);
                        }

                        // Update Salt if provided in Excel
                        if (!string.IsNullOrWhiteSpace(salt))
                        {
                            if (user.Salt != salt)
                            {
                                _logger.LogInformation("Updating user {Email} Salt", user.EmailAddress);
                            }
                            user.Salt = salt;
                            userUpdated = true;
                        }

                        // Update Key if provided in Excel
                        if (!string.IsNullOrWhiteSpace(key))
                        {
                            if (user.Key != key)
                            {
                                _logger.LogInformation("Updating user {Email} Key", user.EmailAddress);
                            }
                            user.Key = key;
                            userUpdated = true;
                        }

                        // Always update the user if any fields were provided in Excel
                        if (userUpdated)
                        {
                            user.ModifiedTime = DateTime.UtcNow;

                            // Explicitly attach and mark the user entity as modified
                            // First detach if already attached to avoid conflicts
                            var entry = _context.Entry(user);
                            if (entry.State != EntityState.Detached)
                            {
                                _logger.LogInformation("User entity for {Email} is already being tracked with state {State} - detaching first",
                                    user.EmailAddress, entry.State);
                                entry.State = EntityState.Detached;
                            }

                            _context.Users.Attach(user);
                            _context.Entry(user).State = EntityState.Modified;

                            _logger.LogInformation("Updated user {Email} with data from Excel - entity state is now {State}",
                                user.EmailAddress, _context.Entry(user).State);
                        }
                        else
                        {
                            _logger.LogInformation("No updates needed for user {Email} - all fields in Excel were empty", user.EmailAddress);
                        }

                        // Validate and parse price
                        decimal price = 0;
                        if (priceIndex > 0 && priceIndex <= worksheet.Dimension.End.Column)
                        {
                            var priceText = worksheet.Cells[row, priceIndex].Text;
                            if (!decimal.TryParse(priceText, out price))
                            {
                                _logger.LogError("Invalid price format: '{PriceText}' in row {Row}", priceText, row);
                                failedRecords.Add(new FailedRecord(row, email, $"Invalid price format: '{priceText}'"));
                                continue;
                            }
                        }
                        else
                        {
                            _logger.LogError("Price column index {Index} is out of range", priceIndex);
                            failedRecords.Add(new FailedRecord(row, email, "Price column not found or out of range"));
                            continue;
                        }

                        // Validate and parse stock capacity
                        int stockCapacity = 0;
                        if (stockCapacityIndex > 0 && stockCapacityIndex <= worksheet.Dimension.End.Column)
                        {
                            var stockCapacityText = worksheet.Cells[row, stockCapacityIndex].Text;
                            if (!int.TryParse(stockCapacityText, out stockCapacity))
                            {
                                _logger.LogError("Invalid stock capacity format: '{StockCapacityText}' in row {Row}", stockCapacityText, row);
                                failedRecords.Add(new FailedRecord(row, email, $"Invalid stock capacity format: '{stockCapacityText}'"));
                                continue;
                            }
                        }
                        else
                        {
                            _logger.LogError("Stock Capacity column index {Index} is out of range", stockCapacityIndex);
                            failedRecords.Add(new FailedRecord(row, email, "Stock Capacity column not found or out of range"));
                            continue;
                        }

                        // First check if we can find a machine by MaintainerNumber (Sim Number)
                        var machine = existingMachines.GetValueOrDefault(machineSimNumber);

                        // If no machine found by MaintainerNumber, check if there's a machine with MachineId from the Excel
                        string machineIdFromExcel = null;
                        if (machineIdIndex > 0 && machineIdIndex <= worksheet.Dimension.End.Column)
                        {
                            machineIdFromExcel = worksheet.Cells[row, machineIdIndex]?.Text?.Trim();
                            if (machine == null && !string.IsNullOrEmpty(machineIdFromExcel) &&
                                existingMachinesByMachineId.TryGetValue(machineIdFromExcel, out var machineByMachineId))
                            {
                                // Found a machine with the MachineId from Excel
                                _logger.LogInformation("Found existing machine with MachineId {MachineId} from Excel", machineIdFromExcel);
                                machine = machineByMachineId;
                            }
                        }
                        else
                        {
                            _logger.LogInformation("Machine ID column not found in Excel file - will try to extract from Machine Name");
                        }

                        // If still no machine found, check if user has a default machine
                        if (machine == null)
                        {
                            // First check if the user already has any default machines by directly searching for machines
                            // where this user is either a Client or Reseller and the machine is a default machine
                            var userDefaultMachines = await _context.Machines
                                .AsNoTracking() // Ensure we get a clean entity
                                .Where(m => m.Name == "VEND0000" && m.MachineId == "0000" && 
                                           (m.ClientId == user.Id || m.ResellerId == user.Id))
                                .ToListAsync();
                                
                            _logger.LogInformation("Found {Count} default machines for user {Email} with ID {UserId}", 
                                userDefaultMachines.Count, user.EmailAddress, user.Id);
                                
                            if (userDefaultMachines.Count > 0)
                            {
                                // Use the first default machine found
                                var defaultMachine = userDefaultMachines.First();
                                _logger.LogInformation("Using existing default machine (ID: {MachineId}) for user {Email}", 
                                    defaultMachine.Id, user.EmailAddress);
                                
                                machine = defaultMachine;
                                machine.MaintainerNumber = machineSimNumber;
                                
                                // Explicitly detach and reattach to ensure proper tracking
                                var entry = _context.Entry(machine);
                                if (entry.State != EntityState.Detached)
                                {
                                    _logger.LogInformation("Default machine is already tracked with state {State} - detaching", entry.State);
                                    entry.State = EntityState.Detached;
                                }
                                
                                _context.Machines.Attach(machine);
                                _context.Entry(machine).State = EntityState.Modified;
                                
                                _logger.LogInformation("Updated default machine (ID: {MachineId}) with Sim Number {SimNumber}", 
                                    machine.Id, machineSimNumber);
                            }
                        }

                        // If still no machine found, look for default machines assigned to this user (only for Client or Reseller roles)
                        if (machine == null)
                        {
                            // Get user role from Excel or use existing role
                            var defaultMachineRoleFromExcel = roleIndex > 0 && roleIndex <= worksheet.Dimension.End.Column
                                ? worksheet.Cells[row, roleIndex].Text?.Trim()
                                : null;
                            var defaultMachineUserRole = !string.IsNullOrWhiteSpace(defaultMachineRoleFromExcel) ? defaultMachineRoleFromExcel : user.Role?.Trim();

                            // Only search for default machines if user is a Client or Reseller
                            if (defaultMachineUserRole == "Client" || defaultMachineUserRole == "Reseller")
                            {
                                _logger.LogInformation("Looking for default machines assigned to user {Email} with role {Role}", user.EmailAddress, defaultMachineUserRole);

                                // Find default machines where this user is either Client or Reseller
                                var defaultMachines = await _context.Machines
                                    .Where(m => m.Name == "VEND0000" && m.MachineId == "0000" &&
                                              ((defaultMachineUserRole == "Client" && m.ClientId == user.Id) ||
                                               (defaultMachineUserRole == "Reseller" && m.ResellerId == user.Id)))
                                    .ToListAsync();

                                if (defaultMachines.Count > 0)
                                {
                                    // Use the first default machine found
                                    machine = defaultMachines.First();
                                    _logger.LogInformation("Found default machine (ID: {MachineId}, Name: {MachineName}) assigned to user {Email} with role {Role}",
                                        machine.Id, machine.Name, user.EmailAddress, defaultMachineUserRole);

                                    // Extract MachineId from Machine Name if possible
                                    var defaultMachineName = worksheet.Cells[row, machineNameIndex]?.Text?.Trim();
                                    if (!string.IsNullOrEmpty(defaultMachineName) && defaultMachineName.Contains('_'))
                                    {
                                        var defaultExtractedMachineId = defaultMachineName.Split('_').LastOrDefault()?.Trim();
                                        if (!string.IsNullOrEmpty(defaultExtractedMachineId))
                                        {
                                            _logger.LogInformation("Updating default machine MachineId from '0000' to '{NewMachineId}' extracted from Machine Name",
                                                defaultExtractedMachineId);
                                            machine.MachineId = defaultExtractedMachineId;
                                            machine.Name = defaultMachineName;
                                        }
                                    }

                                    _context.Machines.Attach(machine);
                                    _context.Entry(machine).State = EntityState.Modified;
                                }
                                else
                                {
                                    _logger.LogInformation("No default machines found for user {Email} with role {Role}", user.EmailAddress, defaultMachineUserRole);
                                }
                            }
                            else
                            {
                                _logger.LogInformation("Skipping default machine search for user {Email} with role {Role} - not a Client or Reseller",
                                    user.EmailAddress, defaultMachineUserRole);
                            }
                        }

                        if (machine == null)
                        {
                            // First check if the user already has any default machines by directly searching for machines
                            // where this user is either a Client or Reseller and the machine is a default machine
                            var userDefaultMachines = await _context.Machines
                                .AsNoTracking() // Ensure we get a clean entity
                                .Where(m => m.Name == "VEND0000" && m.MachineId == "0000" && 
                                           (m.ClientId == user.Id || m.ResellerId == user.Id))
                                .ToListAsync();
                                
                            _logger.LogInformation("Found {Count} default machines for user {Email} with ID {UserId}", 
                                userDefaultMachines.Count, user.EmailAddress, user.Id);
                                
                            if (userDefaultMachines.Count > 0)
                            {
                                // Use the first default machine found
                                var defaultMachine = userDefaultMachines.First();
                                _logger.LogInformation("Using existing default machine (ID: {MachineId}) for user {Email}", 
                                    defaultMachine.Id, user.EmailAddress);
                                
                                machine = defaultMachine;
                                machine.MaintainerNumber = machineSimNumber;
                                
                                // Explicitly detach and reattach to ensure proper tracking
                                var entry = _context.Entry(machine);
                                if (entry.State != EntityState.Detached)
                                {
                                    _logger.LogInformation("Default machine is already tracked with state {State} - detaching", entry.State);
                                    entry.State = EntityState.Detached;
                                }
                                
                                _context.Machines.Attach(machine);
                                _context.Entry(machine).State = EntityState.Modified;
                                
                                _logger.LogInformation("Updated default machine (ID: {MachineId}) with Sim Number {SimNumber}", 
                                    machine.Id, machineSimNumber);
                            }
                            else
                            {
                                // Create new machine with data from Excel
                                _logger.LogInformation("Creating new machine with Sim Number {SimNumber}", machineSimNumber);

                                machine = new Machine
                                {
                                    Id = Guid.NewGuid().ToString(),
                                    CreatedTime = DateTime.UtcNow,
                                    MachineId = "0000",
                                    Name = "VEND0000",
                                    MaintainerNumber = machineSimNumber
                                };

                                _context.Machines.Add(machine);
                            }
                        }
                        else
                        {
                            // For existing machines, check if the Excel machine name/ID is different
                            var currentExcelMachineName = worksheet.Cells[row, machineNameIndex]?.Text?.Trim();
                            var extractedMachineId = string.Empty;
                            
                            if (!string.IsNullOrEmpty(currentExcelMachineName) && currentExcelMachineName.Contains('_'))
                            {
                                extractedMachineId = currentExcelMachineName.Split('_').LastOrDefault()?.Trim();
                                
                                // If the extracted ID from Excel is different from the existing machine's ID,
                                // create a new machine instead of updating the existing one
                                if (!string.IsNullOrEmpty(extractedMachineId) && machine.MachineId != extractedMachineId)
                                {
                                    _logger.LogInformation("Excel contains different machine ID ({ExcelId}) than existing machine ({ExistingId}) - creating new machine", 
                                        extractedMachineId, machine.MachineId);
                                    
                                    // Create new machine with the correct name and ID from Excel
                                    machine = new Machine
                                    {
                                        Id = Guid.NewGuid().ToString(),
                                        CreatedTime = DateTime.UtcNow,
                                        MachineId = extractedMachineId,
                                        Name = currentExcelMachineName,
                                        MaintainerNumber = machineSimNumber
                                    };
                                    
                                    _context.Machines.Add(machine);
                                }
                                else
                                {
                                    // Update existing machine
                                    _logger.LogInformation("Updating existing machine with Sim Number {SimNumber}, MachineId {MachineId}, Name {MachineName}",
                                        machineSimNumber, machine.MachineId, machine.Name);

                                    _context.Machines.Attach(machine);
                                    _context.Entry(machine).State = EntityState.Modified;
                                }
                            }
                            else
                            {
                                // Update existing machine
                                _logger.LogInformation("Updating existing machine with Sim Number {SimNumber}, MachineId {MachineId}, Name {MachineName}",
                                    machineSimNumber, machine.MachineId, machine.Name);

                                _context.Machines.Attach(machine);
                                _context.Entry(machine).State = EntityState.Modified;
                            }
                        }

                        // Update machine details
                        // Only update Name if it's a new machine (for existing machines, keep the current name)
                        var excelMachineName = worksheet.Cells[row, machineNameIndex]?.Text?.Trim();
                        if (machine.Name == "VEND0000" && !string.IsNullOrEmpty(excelMachineName))
                        {
                            _logger.LogInformation("Updating machine name from default 'VEND0000' to '{MachineName}'", excelMachineName);
                            machine.Name = excelMachineName;
                        }
                        else
                        {
                            _logger.LogInformation("Keeping existing machine name: '{MachineName}' (Excel value: '{ExcelMachineName}')",
                                machine.Name, excelMachineName);
                        }

                        // Update MachineId if we can extract it from the Machine Name
                        if (!string.IsNullOrEmpty(excelMachineName) && excelMachineName.Contains('_'))
                        {
                            var extractedMachineId = excelMachineName.Split('_').LastOrDefault()?.Trim();
                            if (!string.IsNullOrEmpty(extractedMachineId) && machine.MachineId != extractedMachineId)
                            {
                                _logger.LogInformation("Updating MachineId from '{OldMachineId}' to '{NewMachineId}' extracted from Machine Name",
                                    machine.MachineId, extractedMachineId);
                                machine.MachineId = extractedMachineId;
                            }
                        }

                        // If MachineId column is present in Excel, use that value
                        if (machineIdIndex > 0 && machineIdIndex <= worksheet.Dimension.End.Column)
                        {
                            var machineIdValueFromExcel = worksheet.Cells[row, machineIdIndex]?.Text?.Trim();
                            if (!string.IsNullOrEmpty(machineIdValueFromExcel) && machine.MachineId != machineIdValueFromExcel)
                            {
                                _logger.LogInformation("Updating MachineId from '{OldMachineId}' to '{NewMachineId}' from Excel MachineId column",
                                    machine.MachineId, machineIdValueFromExcel);
                                machine.MachineId = machineIdValueFromExcel;
                            }
                        }

                        // Validate location field
                        if (locationIndex > 0 && locationIndex <= worksheet.Dimension.End.Column)
                        {
                            machine.Location = worksheet.Cells[row, locationIndex].Text;
                        }
                        else
                        {
                            _logger.LogWarning("Location column index {Index} is out of range - using empty location", locationIndex);
                            machine.Location = string.Empty;
                        }
                        machine.Price = price;
                        machine.AvailableStock = stockCapacity;
                        machine.TotalStockCapacity = stockCapacity;
                        machine.MaintainerEmail = user.EmailAddress;
                        machine.Status = "Active";
                        machine.LastHeartBeatTime = DateTime.UtcNow;
                        machine.ModifiedTime = DateTime.UtcNow;

                        // Set ClientId or ResellerId based on role
                        // Use the role from Excel if it was provided, otherwise use the user's existing role
                        var roleFromExcel = roleIndex > 0 && roleIndex <= worksheet.Dimension.End.Column
                            ? worksheet.Cells[row, roleIndex].Text?.Trim()
                            : null;

                        _logger.LogInformation("Role from Excel: '{RoleFromExcel}', User's existing role: '{ExistingRole}'",
                            roleFromExcel, user.Role);

                        var userRole = !string.IsNullOrWhiteSpace(roleFromExcel) ? roleFromExcel : user.Role?.Trim();

                        _logger.LogInformation("Setting machine relationship for user {Email} with role {Role}", user.EmailAddress, userRole);

                        // Check if this user was previously associated with this machine as a Client or Reseller
                        bool wasClient = machine.ClientId == user.Id;
                        bool wasReseller = machine.ResellerId == user.Id;

                        switch (userRole)
                        {
                            case "Client":
                                machine.ClientId = user.Id;
                                machine.ResellerId = null;
                                _logger.LogInformation("User {Email} with role Client - setting ClientId={Id}", user.EmailAddress, user.Id);
                                break;
                            case "Reseller":
                                machine.ResellerId = user.Id;
                                machine.ClientId = null;
                                _logger.LogInformation("User {Email} with role Reseller - setting ResellerId={Id}", user.EmailAddress, user.Id);
                                break;
                            default:
                                // For other roles, remove any previous ClientId or ResellerId associations
                                if (wasClient)
                                {
                                    _logger.LogInformation("User {Email} role changed from Client to {Role} - removing ClientId", user.EmailAddress, userRole);
                                    machine.ClientId = null;
                                }
                                if (wasReseller)
                                {
                                    _logger.LogInformation("User {Email} role changed from Reseller to {Role} - removing ResellerId", user.EmailAddress, userRole);
                                    machine.ResellerId = null;
                                }

                                if (!wasClient && !wasReseller)
                                {
                                    _logger.LogWarning("Role '{Role}' not recognized as Client or Reseller for user {Email} - not updating ClientId or ResellerId", userRole, user.EmailAddress);
                                }
                                break;
                        }

                        data.Add(machine);
                    }
                    catch (Exception ex)
                    {
                        // Log the full exception details for debugging
                        _logger.LogError(ex, "Error processing row {Row} for email {Email}: {Message}", row, email, ex.Message);

                        // Add a more descriptive error message
                        var errorMessage = ex.Message;
                        if (ex.Message.Contains("out of range"))
                        {
                            errorMessage = "Column out of range error. Please ensure your Excel file has all required columns.";
                        }

                        failedRecords.Add(new FailedRecord(row, email, $"Error: {errorMessage}"));
                    }
                }

                // We'll save changes in the SaveToDatabase method
                // Just prepare the data here
            }
        }

        // Return success if we processed any records successfully
        string message = data.Count > 0
            ? $"Successfully processed {data.Count} records" + (failedRecords.Count > 0 ? $" with {failedRecords.Count} failures" : "")
            : "No records were successfully processed";

        return (data.Count > 0, message, data, failedRecords);
    }

    private static bool IsColumnPresent(List<string> headers, string requiredColumn)
    {
        if (headers.Any(h => h.Equals(requiredColumn, StringComparison.OrdinalIgnoreCase)))
            return true;

        if (ColumnVariations.TryGetValue(requiredColumn, out var variations))
        {
            return headers.Any(h => variations.Any(v => v.Equals(h, StringComparison.OrdinalIgnoreCase)));
        }

        return headers.Any(h => NormalizeColumnName(h).Equals(NormalizeColumnName(requiredColumn), StringComparison.OrdinalIgnoreCase));
    }

    private static int GetColumnIndex(List<string> headers, string columnName)
    {
        // Try exact match first
        int index = headers.FindIndex(h => h.Equals(columnName, StringComparison.OrdinalIgnoreCase));
        if (index != -1)
        {
            Console.WriteLine($"Found exact match for column '{columnName}' at index {index + 1}");
            return index + 1;
        }

        // Try variations
        if (ColumnVariations.TryGetValue(columnName, out var variations))
        {
            Console.WriteLine($"Looking for variations of '{columnName}': {string.Join(", ", variations)}");
            index = headers.FindIndex(h => variations.Any(v => v.Equals(h, StringComparison.OrdinalIgnoreCase)));
            if (index != -1)
            {
                var matchedVariation = variations.First(v => headers[index].Equals(v, StringComparison.OrdinalIgnoreCase));
                Console.WriteLine($"Found variation match '{matchedVariation}' for column '{columnName}' at index {index + 1}");
                return index + 1;
            }
        }

        // Try normalized match
        var normalizedSearch = NormalizeColumnName(columnName);
        Console.WriteLine($"Looking for normalized match of '{columnName}' (normalized: '{normalizedSearch}')");
        index = headers.FindIndex(h => NormalizeColumnName(h).Equals(normalizedSearch, StringComparison.OrdinalIgnoreCase));

        if (index != -1)
        {
            Console.WriteLine($"Found normalized match '{headers[index]}' (normalized: '{NormalizeColumnName(headers[index])}') for column '{columnName}' at index {index + 1}");
        }

        // If index is still -1, the column wasn't found
        if (index == -1)
        {
            throw new InvalidOperationException($"Required column '{columnName}' not found in Excel file. Available columns: {string.Join(", ", headers)}");
        }

        return index + 1;
    }

    private static int GetOptionalColumnIndex(List<string> headers, string columnName)
    {
        try
        {
            return GetColumnIndex(headers, columnName);
        }
        catch (InvalidOperationException)
        {
            // Column not found, return -1 to indicate it's not available
            return -1;
        }
    }

    private static string NormalizeColumnName(string columnName)
    {
        return string.Concat(columnName.ToLower().Where(char.IsLetterOrDigit));
    }

    private static bool IsRowEmpty(ExcelWorksheet worksheet, int row)
    {
        for (int col = 1; col <= worksheet.Dimension.End.Column; col++)
        {
            if (!string.IsNullOrWhiteSpace(worksheet.Cells[row, col].Text))
                return false;
        }
        return true;
    }

    private static bool IsValidEmail(string email)
    {
        try {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch {
            return false;
        }
    }

    public async Task<(bool success, int savedCount)> SaveToDatabase(List<Machine> data)
    {
        // Use a transaction to ensure all changes are saved together
        using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            // Log the entities being tracked before saving
            var userEntries = _context.ChangeTracker.Entries<User>()
                .Where(e => e.State == EntityState.Modified)
                .ToList();

            _logger.LogInformation("Saving changes for {UserCount} modified users", userEntries.Count);

            foreach (var entry in userEntries)
            {
                var user = entry.Entity;
                _logger.LogInformation("User changes for {Email}: Role={Role}, FirstName={FirstName}, LastName={LastName}",
                    user.EmailAddress, user.Role, user.FirstName, user.LastName);

                // Ensure the entity is marked as modified
                entry.State = EntityState.Modified;
            }

            var machineEntries = _context.ChangeTracker.Entries<Machine>()
                .Where(e => e.State == EntityState.Modified || e.State == EntityState.Added)
                .ToList();

            _logger.LogInformation("Saving changes for {MachineCount} machines ({AddedCount} new, {ModifiedCount} modified)",
                machineEntries.Count,
                machineEntries.Count(e => e.State == EntityState.Added),
                machineEntries.Count(e => e.State == EntityState.Modified));

            // Save all changes (both user and machine updates)
            var changesCount = await _context.SaveChangesAsync();
            await transaction.CommitAsync();

            _logger.LogInformation("Successfully saved {Count} changes to database ({MachineCount} machines)",
                changesCount, data.Count);
            return (true, data.Count);
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            _logger.LogError(ex, "Failed to save changes to database: {Message}", ex.Message);
            return (false, 0);
        }
    }


}
