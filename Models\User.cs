namespace VendorMonitor.Api.Models;

public class User : AuditableEntity
{
    public string EmailAddress { get; set; }
    public string PasswordHash { get; set; }
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public string PhoneNumber { get; set; }
    public string Role { get; set; }
    public string ProfilePicture { get; set; }
    public string ResellerId { get; set; }
    public string Key { get; set; }
    public string Salt { get; set; }
    public string OwnedBy { get; set; }
    public string OTP { get; set; }
    public DateTime OTPExpiry { get; set; }
    public string SubMerchantId { get; set; }
    public bool IsActive { get; set; }
    public bool IsDeleted { get; set; }
}