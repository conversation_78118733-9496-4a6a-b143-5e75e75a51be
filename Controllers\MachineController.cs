using System.Collections;
using System.Text.Json.Nodes;
using VendorMonitor.Api.Common;
using VendorMonitor.Api.Infrastructure;
using VendorMonitor.Api.Services;
using VendorMonitor.Api.ViewModels;
using VendorMonitor.Api.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System.Security.Claims;
using ExcelMigrationApi.Services;

namespace VendorMonitor.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class MachineController(MachineService machineService, EasebuzzService easebuzzService, IExcelService excelService) : ControllerBase
{

    // [HttpGet]
    // [Authorize]
    // public async Task<IActionResult> GetMachines(int page = 0, int size = 20)
    // {
    //     var userId = HttpContext.User.Claims.FirstOrDefault(claim => claim.Type == ClaimTypes.NameIdentifier)?.Value;
    //     var role = HttpContext.User.Claims.FirstOrDefault(claim => claim.Type == ClaimTypes.Role)?.Value;
    //     //var response = await machineService.GetMachines(page, size > 20 ? 20 : size, userId, role);
    //     return new JsonResult(new
    //     {
    //         Status = 200,
    //         // Result = new
    //         // {
    //         //     Count = response.Item1,
    //         //     Items = response.Item2
    //         // }
    //     });
    // }

    [HttpPut]
    [Authorize(Policy = "Manufacturer")]
    public async Task<IActionResult> CreateMachines(CreateMachineViewModel assignMachineViewModel)
    {
        var userId = HttpContext.User.Claims.FirstOrDefault(claim => claim.Type == ClaimTypes.NameIdentifier)?.Value;
        await machineService.CreateMachines(userId, assignMachineViewModel);
        return new JsonResult(new
        {
        });
    }

    [HttpGet]
    [Authorize]
    [Route("idnames")]
    public async Task<IActionResult> GetIdNames(string userId)
    {
        return new JsonResult(new
        {
            Result = await machineService.GetIdNames(userId)
        });
    }

    [HttpPut]
    [Route("{id}/assignment")]
    [Authorize(Policy = "ManufacturerOrReseller")]
    public async Task<IActionResult> AssignMachine(string id, AssignMachineViewModel assignMachineViewModel)
    {
        await machineService.AssignMachine(id, assignMachineViewModel);
        return Ok(new { });
    }

    [HttpPut]
    [Route("{id}/resellerDetails")]
    [Authorize(Policy = "ManufacturerOrReseller")]
    public async Task<IActionResult> UpdateResellerDetails(string id, ResellerUpdateViewModel resellerUpdateViewModel)
    {
        await machineService.ResellerUpdateMachine(id, resellerUpdateViewModel);
        return Ok(new { });
    }

    [HttpPut]
    [Route("{id}/clientDetails")]
    [Authorize]
    public async Task<IActionResult> UpdateClientDetails(string id, ClientUpdateViewModel clientUpdateViewModel)
    {
        await machineService.ClientUpdateMachine(id, clientUpdateViewModel);
        return Ok(new { });
    }


    [HttpPost]
    [Route("{machineId}/qr-code")]
    [Authorize]
  public async Task<IActionResult> CreateVirtualAccountAndGenerateQr(string machineId)
{
    try
    {
        var userId = HttpContext.User.Claims.FirstOrDefault(claim => claim.Type == ClaimTypes.NameIdentifier)?.Value;
        var qrCodeUrl = await machineService.CreateVirtualAccountAndGenerateQr(userId, machineId);

        if (string.IsNullOrEmpty(qrCodeUrl))
        {
            return BadRequest("Failed to generate QR code.");
        }
        return Ok(new { qrCodeUrl });
    }
    catch (Exception)
    {
        return StatusCode(500, new { Message = "An error occurred while generating the QR code." });
    }
}

    [HttpPost]
    [Route("status")]
    public async Task<IActionResult> UpdateStatus(MachineStatusViewModel statusViewModel)
    {
        await easebuzzService.UpdateStatus(statusViewModel);
        return Ok();

    }

     [HttpPost("upload")]
    public async Task<IActionResult> UploadExcel(IFormFile file)
    {
        try
        {
            if (file == null || file.Length == 0)
                return BadRequest(new { Message = "No file uploaded" });

            // Check file extension as well as content type
            var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
            if ((!file.ContentType.Contains("excel") && !file.ContentType.Contains("spreadsheet")) ||
                (fileExtension != ".xlsx" && fileExtension != ".xls"))
            {
                return BadRequest(new { Message = "Invalid file format. Please upload an Excel file (.xlsx or .xls)" });
            }

            (bool isValid, string message, List<Machine> data, List<FailedRecord> failedRecords) = await excelService.ProcessExcelFile(file);

            // If we have no valid records and some failures, return bad request with failures
            if (!isValid && failedRecords.Count > 0)
            {
                return BadRequest(new {
                    Message = message,
                    FailedRecords = failedRecords
                });
            }

            // If we have no valid records and no failures, return bad request with message
            if (!isValid)
                return BadRequest(new { Message = message });

            // Try to save valid records
            var (success, savedCount) = await excelService.SaveToDatabase(data);
            if (!success)
                return StatusCode(500, new { Message = "Failed to save data to database" });

            // Return success response with counts of successful and failed records
            return Ok(new {
                Message = savedCount==0 ? "No records were successfully processed":  $"Successfully imported {savedCount} records",
                SuccessCount = savedCount,
                FailedRecords = failedRecords
            });
        }
        catch (Exception ex)
        {
            // Log the exception
            return StatusCode(500, new { Message = $"An error occurred while processing the Excel file: {ex.Message}" });
        }
    }
}
