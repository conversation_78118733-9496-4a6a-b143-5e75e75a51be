
using System.Security.Claims;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using VendorMonitor.Api.Common;
using VendorMonitor.Api.Models;
using VendorMonitor.Api.Utilities;
using Microsoft.EntityFrameworkCore.ChangeTracking;

namespace VendorMonitor.Api.Data;

public class ApplicationDbContext : DbContext
{
    public readonly GeneralSettings _generalSettings;
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options, IOptions<GeneralSettings> generalSettings) : base(options)
    {
        AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);
        AppContext.SetSwitch("Npgsql.DisableDateTimeInfinityConversions", true);
        _generalSettings = generalSettings.Value;
    }

    public DbSet<Invitation> Invitations { get; set; }
    public DbSet<User> Users { get; set; }
    public DbSet<Sample> Samples { get; set; }
    public DbSet<Transaction> Transactions { get; set; }
    public DbSet<Machine> Machines { get; set; }
    public DbSet<Product> Products { get; set; }


    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // Seed data
        // modelBuilder.Entity<User>().HasData(
        //     new User { Id = _generalSettings.AdminId, FirstName = "VendorMonitor", LastName = "Manufacturer", PasswordHash = EncryptionUtils.HashPassword(_generalSettings.AdminPassword, _generalSettings.PasswordSalt), EmailAddress = _generalSettings.AdminEmail, IsActive = true, IsDeleted = false, Role = "Manufacturer", CreatedBy = "System", ModifiedBy = "System" }
        // );

    }

    public override int SaveChanges()
    {
        var entries = ChangeTracker.Entries()
            .Where(e => e.Entity is AuditableEntity &&
                        (e.State == EntityState.Added || e.State == EntityState.Modified));

        var user = GetUser();

        foreach (var entityEntry in entries)
        {
            var entity = (AuditableEntity)entityEntry.Entity;
            entity.ModifiedTime = DateTime.UtcNow;
            entity.ModifiedBy = user;

            if (entityEntry.State == EntityState.Added)
            {
                entity.CreatedTime = entity.ModifiedTime;
                entity.CreatedBy = user;
            }
        }

        return base.SaveChanges();
    }
    public override ValueTask<EntityEntry<TEntity>> AddAsync<TEntity>(TEntity entity, CancellationToken cancellationToken = default)
    {
        if (entity is AuditableEntity auditableEntity)
        {
            if (string.IsNullOrEmpty(auditableEntity.Id))
            {
                auditableEntity.Id = Guid.NewGuid().ToString();
                auditableEntity.CreatedTime = DateTime.UtcNow;
                auditableEntity.CreatedBy = GetUser();
            }
        }
        return base.AddAsync(entity, cancellationToken);
    }



    private static string GetUser()
    {
        if (HttpContextAccessor.Accessor.HttpContext?.User?.Identity != null)
        {
            return HttpContextAccessor.Accessor.HttpContext.User.Claims.FirstOrDefault(claim => claim.Type == ClaimTypes.NameIdentifier)?.Value;
        }
        else
        {
            return null;
        }
    }
}