using VendorMonitor.Api.Models;
using VendorMonitor.Api.ViewModels;
using AutoMapper;
namespace VendorMonitor.Api;

public class MappingProfile : Profile
{
    public MappingProfile()
    {
        CreateMap<SaveInvitationViewModel, Invitation>().ForMember(_ => _.EmailAddress,
            opt => opt.MapFrom(_ => _.EmailAddress.ToLowerInvariant()));
        CreateMap<Invitation, ListInvitationViewModel>();
        CreateMap<User, ListUserViewModel>();
        CreateMap<User, ProfileViewModel>();
        CreateMap<User, IdNameViewModel>().ForMember(_ => _.Name, _ => _.MapFrom(src => $"{src.FirstName} {src.LastName}"));
        CreateMap<Machine, IdNameViewModel>();
        CreateMap<ProfileViewModel, User>();
        CreateMap<SaveSampleViewModel, Sample>();
        CreateMap<Sample, ListSampleViewModel>();
        CreateMap<Sample, ViewSampleViewModel>();


        CreateMap<SaveUserViewModel, User>();
        CreateMap<User, ListUserViewModel>();
        CreateMap<User, ViewUserViewModel>();

        CreateMap<SaveProductViewModel, Product>();
        CreateMap<Product, ListProductViewModel>();
        CreateMap<Product, ViewProductViewModel>();
        CreateMap<SaveMachineViewModel, Machine>();
        CreateMap<AssignMachineViewModel, Machine>();
        CreateMap<ResellerUpdateViewModel, Machine>();
        CreateMap<ClientUpdateViewModel, Machine>();
        CreateMap<CreateMachineViewModel, Machine>();
        CreateMap<Machine, ListMachineViewModel>();
        CreateMap<Transaction, ListTransactionViewModel>();
        
    }
}