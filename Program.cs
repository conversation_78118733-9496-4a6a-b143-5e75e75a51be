using System.Security.Claims;
using System.Text;
using FluentEmail.MailKitSmtp;
using VendorMonitor.Api;
using VendorMonitor.Api.Common;
using VendorMonitor.Api.Data;
using VendorMonitor.Api.Infrastructure;
using VendorMonitor.Api.Models;
using VendorMonitor.Api.Services;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using SalesPilots.Api.Services;
using Microsoft.EntityFrameworkCore;
using ExcelMigrationApi.Services;

var builder = WebApplication.CreateBuilder(args);
builder.Services.AddDbContext<ApplicationDbContext>(options =>
        options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection")));
var smtpClient = new SmtpClientOptions();
builder.Configuration.GetSection("SmtpClient").Bind(smtpClient);
builder.Services.Configure<MQTTSettings>(builder.Configuration.GetSection("MQTT"));
builder.Services
        .AddFluentEmail("<EMAIL>", "VendorMonitor Admin")
        .AddRazorRenderer()
        .AddMailKitSender(smtpClient);
builder.Services.AddAuthorization(options =>
{
    options.AddPolicy("Manufacturer", policy =>
                      policy.RequireClaim(ClaimTypes.Role, "Manufacturer"));
    options.AddPolicy("Reseller", policy => 
                      policy.RequireClaim(ClaimTypes.Role, "Reseller"));
    options.AddPolicy("ManufacturerOrReseller", policy =>
    policy.RequireAssertion(context =>
        context.User.HasClaim(ClaimTypes.Role, "Reseller") || 
        context.User.HasClaim(ClaimTypes.Role, "Manufacturer")));
});
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme).AddJwtBearer(options =>
{
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuer = true,
        ValidateLifetime = true,
        ValidateAudience = false,
        ValidateIssuerSigningKey = true,
        ValidIssuer = builder.Configuration["Jwt:Issuer"],
        IssuerSigningKey = new
        SymmetricSecurityKey(Encoding.UTF8.GetBytes(builder.Configuration["Jwt:Key"]))
    };
});
LoadConfiguration(builder);
LoadCORSConfiguration(builder);
builder.Services.AddHttpClient();
builder.Services.AddHttpContextAccessor();
builder.Services.AddAutoMapper(options => options.AddProfile<MappingProfile>());
// builder.Services.AddHostedService<BackgroundHostedService>();
// builder.Services.AddHostedService<QueuedHostedService>();
builder.Services.AddHostedService<HiveHostedService>();
builder.Services.AddSingleton<IQueueService, QueueService>();
builder.Services.AddTransient<InvitationService>();
builder.Services.AddTransient<EmailService>();
builder.Services.AddTransient<JWTService>();
builder.Services.AddTransient<UserService>();
builder.Services.AddTransient<SampleService>();
builder.Services.AddTransient<ProductService>();
builder.Services.AddTransient<MachineService>();
builder.Services.AddSingleton<HiveMQTTService>();
builder.Services.AddTransient<EasebuzzService>();
builder.Services.AddTransient<IExcelService, ExcelService>();

// Add services to the container.
builder.Services.AddControllersWithViews();
builder.Services
    .AddSwaggerGen(c =>
    {
        c.SwaggerDoc("v1", new OpenApiInfo { Title = "VendMonitor", Version = "v1" });
        var securityScheme = new OpenApiSecurityScheme
        {
            Name = "JWT Authentication",
            Description = "Enter JWT Bearer token **_only_**",
            In = ParameterLocation.Header,
            Type = SecuritySchemeType.Http,
            Scheme = "bearer", // must be lower case
            BearerFormat = "JWT",
            Reference = new OpenApiReference
            {
                Id = JwtBearerDefaults.AuthenticationScheme,
                Type = ReferenceType.SecurityScheme
            }
        };
        c.AddSecurityDefinition(securityScheme.Reference.Id, securityScheme);
        c.AddSecurityRequirement(new OpenApiSecurityRequirement
        {
            {
                securityScheme, new string[] { }
            }
        });
    }).AddSwaggerGenNewtonsoftSupport();

var app = builder.Build();
HttpContextAccessor.Accessor = app.Services.GetService<IHttpContextAccessor>();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}
app.UseSwagger();
app.UseSwaggerUI(options =>
{
    options.SwaggerEndpoint("/swagger/v1/swagger.json", "v1");
    options.RoutePrefix = "swagger";
});

app.UseHttpsRedirection();
app.UseStaticFiles();
app.UseForwardedHeaders(new ForwardedHeadersOptions
{
    ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto
});

app.UseRouting();
app.UseCors("corsOrigins");
app.UseSingleTenantExceptionHandler();

app.UseAuthentication();
app.UseAuthorization();

app.UseAuthorization();

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

app.Run();

void LoadConfiguration(WebApplicationBuilder builder)
{
    builder.Services.Configure<GeneralSettings>(builder.Configuration.GetSection("General"));
    builder.Services.Configure<JWTSettings>(options => builder.Configuration.GetSection("JWT").Bind(options));
    builder.Services.Configure<SmtpClientOptions>(options => builder.Configuration.GetSection("SmtpClient").Bind(options));
}

void LoadCORSConfiguration(WebApplicationBuilder builder)
{
    builder.Services.AddCors(options =>
    {
        options.AddPolicy(name: "corsOrigins", policy =>
        {
            policy.WithOrigins(builder.Configuration["CORSSettings:Origins"].Split(","))
                .AllowCredentials()
                .AllowAnyHeader()
                .AllowAnyMethod();
        });
    });
}
