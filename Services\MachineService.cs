using System.Collections;
using VendorMonitor.Api.Exceptions;
using VendorMonitor.Api.Models;
using VendorMonitor.Api.Utilities;
using VendorMonitor.Api.ViewModels;
using AutoMapper;
using Microsoft.Extensions.Options;
using VendorMonitor.Api.Data;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Security.Cryptography;
using Microsoft.EntityFrameworkCore;

namespace VendorMonitor.Api.Services;

public class MachineService(ApplicationDbContext dbContext, ProductService productService,UserService userService, IMapper mapper)
{
    public async Task CreateMachines(string userId, CreateMachineViewModel createMachineViewModel)
    {
        if (createMachineViewModel.Quantity <= 0)
        {
            throw new BadRequestException("Quantity is required and must be greater than zero.");
        }
        var machineIds = new List<string>();
        for (int i = 0; i < createMachineViewModel.Quantity; i++)
        {
            await dbContext.AddAsync(new Machine()
            {
                ResellerId = String.IsNullOrEmpty(createMachineViewModel.ResellerId) ? userId : createMachineViewModel.ResellerId,
                MachineId = "0000",
                Name = "VEND0000"
            });
        }
        await dbContext.SaveChangesAsync();
    }

    public async Task<Tuple<long, List<ListMachineViewModel>>> GetMachines(int page, int size, string userId, string role, MachineRequestViewModel machineRequestViewModel, string quickSearch)
    {
        IQueryable<Machine> query = dbContext.Machines;
        machineRequestViewModel ??= new MachineRequestViewModel();
        if (role == "Manufacturer")
        {
            if (!string.IsNullOrEmpty(machineRequestViewModel.ResellerId))
            {
                query = query.Where(m => m.ResellerId == machineRequestViewModel.ResellerId);
            }
            if (!string.IsNullOrEmpty(machineRequestViewModel.ClientId))
            {
                query = query.Where(m => m.ClientId == machineRequestViewModel.ClientId);
            }
        }
        else if (role == "Reseller")
        {
            query = query.Where(m => m.ResellerId == userId);

            if (!string.IsNullOrEmpty(machineRequestViewModel.ClientId))
            {
                query = query.Where(m => m.ClientId == machineRequestViewModel.ClientId);
            }
        }
        else if (role == "Client")
        {
            query = query.Where(m => m.ClientId == userId);
        }

        if (!string.IsNullOrEmpty(quickSearch))
        {
            quickSearch = quickSearch.ToLower().Trim();

            query = query.Where(m =>
                m.MachineId.ToLower().Contains(quickSearch) ||
                m.Name.ToLower().Contains(quickSearch) ||
                m.MaintainerEmail.ToLower().Contains(quickSearch) ||
                m.MaintainerNumber.Contains(quickSearch) ||
                m.AvailableStock.ToString().Contains(quickSearch)
            );
        }

        var machines = await query.Skip(page * size).Take(size).ToListAsync();
        var result = machines.Select(m =>
      {
          var viewModel = mapper.Map<ListMachineViewModel>(m);
          var c  =(DateTime.UtcNow - m.LastHeartBeatTime).TotalMinutes;
          viewModel.Status = (DateTime.UtcNow - m.LastHeartBeatTime).TotalMinutes <= 5
              ? "ONLINE"  // Machine is online if the heartbeat is recent (less than 5 minutes ago)
              : "OFFLINE"; 
          return viewModel;
      }).ToList();

        var count = await query.CountAsync();

        return new Tuple<long, List<ListMachineViewModel>>(count, result);
    }

    public async Task<List<IdNameViewModel>> GetIdNames(string userId)
    {
        return mapper.Map<List<IdNameViewModel>>(await dbContext.Machines.Where(m => m.ClientId == userId).ToListAsync());
    }

    public async Task<Tuple<long, List<ListTransactionViewModel>>> GetTransactions(int page, int size, string userId, string role, TransactionRequestViewModel transactionRequestViewModel)
    {
        IQueryable<Transaction> query = dbContext.Transactions.Include(x => x.Machine);
        if (role == "Manufacturer")
        {
            if (!string.IsNullOrEmpty(transactionRequestViewModel?.ResellerId))
            {
                query = query.Where(t => t.Machine.ResellerId == transactionRequestViewModel.ResellerId);
            }
            if (!string.IsNullOrEmpty(transactionRequestViewModel?.ClientId))
            {
                query = query.Where(t => t.Machine.ClientId == transactionRequestViewModel.ClientId);
            }
        }
        else if (role == "Reseller")
        {
            query = query.Where(t => t.Machine.ResellerId == userId);

            if (!string.IsNullOrEmpty(transactionRequestViewModel?.ClientId))
            {
                query = query.Where(t => t.Machine.ClientId == transactionRequestViewModel.ClientId);
            }
        }
        else if (role == "Client")
        {
            query = query.Where(t => t.Machine.ClientId == userId);
        }
        if (!string.IsNullOrEmpty(transactionRequestViewModel?.MachineId))
        {
            query = query.Where(t => t.Machine.MachineId == transactionRequestViewModel.MachineId);
        }

        if (!string.IsNullOrEmpty(transactionRequestViewModel?.PaymentType.ToString()))
        {
            query = query.Where(t => t.PaymentMode == transactionRequestViewModel.PaymentType.ToString());
        }

        if (!string.IsNullOrEmpty(transactionRequestViewModel?.StartDate) && DateTime.TryParse(transactionRequestViewModel?.StartDate, out var startDate))
        {
            query = query.Where(t => t.CreatedTime >= startDate);
        }

        if (!string.IsNullOrEmpty(transactionRequestViewModel?.EndDate) && DateTime.TryParse(transactionRequestViewModel?.EndDate, out var endDate))
        {
            query = query.Where(t => t.CreatedTime <= endDate);
        }

        var transactions = await query
          .OrderByDescending(t => t.CreatedTime)
          .Skip(page * size)
          .Take(size)
          .ToListAsync();
        var count = await query.CountAsync();
        var result = mapper.Map<List<ListTransactionViewModel>>(transactions);
        return new Tuple<long, List<ListTransactionViewModel>>(count, result);
    }


    public async Task AssignMachine(string machineId, AssignMachineViewModel assignMachineViewModel)
    {
        var machine = await GetMachineInternal(machineId);
        var product = await productService.GetProductInternal(assignMachineViewModel.ProductId);
        mapper.Map(assignMachineViewModel, machine);
        machine.Name = $"{product.Name.Substring(0, 4)}_{machine.MachineId}";
        var user = await userService.GetUserInternal(assignMachineViewModel.ClientId);
        machine.MaintainerEmail = user.EmailAddress;
        machine.MaintainerNumber = user.PhoneNumber;

        await dbContext.SaveChangesAsync();
    }

    public async Task ResellerUpdateMachine(string machineId, ResellerUpdateViewModel resellerUpdateViewModel)
    {
        var machine = await GetMachineInternal(machineId);
        mapper.Map(resellerUpdateViewModel, machine);
        await dbContext.SaveChangesAsync();
    }

    public async Task ClientUpdateMachine(string machineId, ClientUpdateViewModel clientUpdateViewModel)
    {
        var machine = await GetMachineInternal(machineId);
        mapper.Map(clientUpdateViewModel, machine);
        await dbContext.SaveChangesAsync();
    }

    internal async Task<Machine> GetMachineInternal(string machineId)
    {
        var Machine = await dbContext.Machines.Where(_ => _.Id == machineId).FirstOrDefaultAsync();
        if (Machine == null)
        {
            throw new NotFoundException("Machine not found");
        }
        return Machine;
    }

    public async Task DeleteMachine(string machineId)
    {
        var machine = await GetMachineInternal(machineId);
        dbContext.Machines.Remove(machine);
        await dbContext.SaveChangesAsync();
    }

    public async Task<string> CreateVirtualAccountAndGenerateQr(string userId, string machineId)
    {
        var user = await GetUser(userId);
        var machine = await GetMachineInternal(machineId);

        if (machine.Price <= 0)
        {
            throw new BadRequestException("Price is required to generate a QR code and must be greater than zero.");
        }

        var requestBody = CreateVirtualAccountRequest(user, machine);
        string hashedValue = GenerateAuthorizationHeader(user.Key, machine.Name, user.Salt);

        string response = await SendHttpRequest("https://wire.easebuzz.in/api/v1/insta-collect/virtual_accounts/", requestBody, hashedValue);
        machine.VirtualAccountId = ParseVirtualAccountId(response);

        await dbContext.SaveChangesAsync();

        return await GenerateDynamicQr(user, machineId);
    }

    private async Task<string> GenerateDynamicQr(User user, string machineId)
    {
        var machine = await GetMachineInternal(machineId);
        if (string.IsNullOrWhiteSpace(machine.VirtualAccountId))
        {
            throw new BadRequestException("User does not have a Virtual Account to generate a QR code.");
        }

        return await CreateCollectRequest(user, machine);
    }

    private async Task<string> CreateCollectRequest(User user, Machine machine)
    {
        var requestBody = CreateCollectRequestBody(user, machine);
        string hashedValue = GenerateAuthorizationHeader(user.Key, machine.VirtualAccountId, user.Salt);

        string response = await SendHttpRequest($"https://wire.easebuzz.in/api/v1/insta-collect/virtual_accounts/{machine.VirtualAccountId}/collect_request/", requestBody, hashedValue, user.Key);
        machine.QrCodeUrl = ParseQrCodeUrl(response);
        await dbContext.SaveChangesAsync();

        return machine.QrCodeUrl;
    }

    private static object CreateVirtualAccountRequest(User user, Machine machine) => new
    {
        key = user.Key,
        label = machine.Name
    };

    private static object CreateCollectRequestBody(User user, Machine machine) => new
    {
        key = user.Key,
        phone = user.PhoneNumber,
        collect_request_type = new[] { "upi_handle" },
        amount = machine.Price
    };

    private async Task<string> SendHttpRequest(string url, object requestBody, string hashedValue, string apiKey = null)
    {
        var jsonContent = new StringContent(JsonSerializer.Serialize(requestBody), Encoding.UTF8, "application/json");

        using var httpClient = new HttpClient();
        httpClient.DefaultRequestHeaders.Add("Authorization", hashedValue);
        if (!string.IsNullOrEmpty(apiKey))
        {
            httpClient.DefaultRequestHeaders.Add("WIRE-API-KEY", apiKey);
        }

        var response = await httpClient.PostAsync(url, jsonContent);
        var responseContent = await response.Content.ReadAsStringAsync();

        if (!response.IsSuccessStatusCode)
        {
            throw new BadRequestException($"Request failed: {responseContent}");
        }

        return responseContent;
    }

    private static string ParseVirtualAccountId(string responseContent)
    {
        using var doc = JsonDocument.Parse(responseContent);
        var root = doc.RootElement;

        if (root.TryGetProperty("success", out var successElement) && successElement.GetBoolean())
        {
            return root.GetProperty("data")
                       .GetProperty("virtual_account")
                       .GetProperty("id")
                       .GetString();
        }
        throw new BadRequestException(root.GetProperty("message").GetString());
    }

    private static string ParseQrCodeUrl(string responseContent)
    {
        using var doc = JsonDocument.Parse(responseContent);
        var root = doc.RootElement;

        if (root.TryGetProperty("success", out var successElement) && successElement.GetBoolean())
        {
            return root.GetProperty("data")
                       .GetProperty("virtual_account")
                       .GetProperty("upi_qrcode_url")
                       .GetString();
        }
        throw new BadRequestException(root.GetProperty("message").GetString());
    }

    private string GenerateAuthorizationHeader(string key, string value, string salt)
    {
        string hashInput = $"{key}|{value}|{salt}";
        return GenerateHash(hashInput);
    }

    private string GenerateHash(string hashInput)
    {
        return BitConverter.ToString(ComputeHash512(Encoding.UTF8.GetBytes(hashInput))).Replace("-", "");
    }

    private byte[] ComputeHash512(byte[] payload)
    {
        using SHA512Managed hashString = new SHA512Managed();
        return hashString.ComputeHash(payload);
    }

    private async Task<User> GetUser(string userId)
    {
        var user = await dbContext.Users
            .Where(x => x.Id == userId)
            .Select(x => new User { Key = x.Key, Salt = x.Salt, PhoneNumber = x.PhoneNumber, OwnedBy = x.OwnedBy })
            .FirstOrDefaultAsync();
        if (user == null)
        {
            throw new NotFoundException("User not found.");
        }
        if (string.IsNullOrWhiteSpace(user.PhoneNumber))
        {
            throw new BadRequestException("PhoneNumber is required to generate QR.");
        }
        if (string.IsNullOrWhiteSpace(user.Key) || string.IsNullOrWhiteSpace(user.Salt))
        {
            throw new BadRequestException("Both the key and salt for this user are required. To obtain a key and salt, please create an account in EaseBuzz.");
        }
        return user;
    }

    public async Task InitiateRefund(string easebuzzTransactionId)
    {
        Console.WriteLine($"Initiating refund for transaction {easebuzzTransactionId}");

        var transaction = await dbContext.Transactions.Include(_ => _.Machine)
                          .FirstOrDefaultAsync(x => x.EasebuzzTransactionId == easebuzzTransactionId);
        if (transaction == null)
        {
            throw new BadRequestException("Transaction not found.");
        }
        var user = await GetUser(transaction.Machine.ClientId);
        if (user == null)
        {
            throw new BadRequestException("User not found.");
        }

        var userMaster = await GetUser(user.OwnedBy);
        if (userMaster == null)
        {
            throw new BadRequestException("Master user not found.");
        }

        string hashedValue = GenerateAuthorizationHeader(user.Key, $"{easebuzzTransactionId}|{transaction.AmountPaid}", user.Salt);
        var requestBody = RefundRequestBody(user.Key, transaction.AmountPaid, transaction.PaymentMode);

        string responseContent = await SendRefundRequest(easebuzzTransactionId, requestBody, hashedValue, userMaster.Key);
        await ProcessResponse(responseContent, transaction);
    }

    private static object RefundRequestBody(string key, decimal amount, string paymentMode) => new
    {
        key = key,
        amount = amount,
        payment_mode = paymentMode
    };

    private async Task<string> SendRefundRequest(string easebuzzTransactionId, object requestBody, string hashedValue, string merchantKey)
    {
        return await SendHttpRequest($"https://wire.easebuzz.in/api/v1/insta-collect/transactions/{easebuzzTransactionId}/initiate_refund/", requestBody, hashedValue, merchantKey);
    }

    private async Task ProcessResponse(string responseContent, Transaction transaction)
    {
        using var doc = JsonDocument.Parse(responseContent);
        var root = doc.RootElement;
        transaction.Status = root.TryGetProperty("success", out var successElement) && successElement.GetBoolean()
           ? TransactionStatus.Refunded
           : TransactionStatus.RefundFailed;

        await dbContext.SaveChangesAsync();

        if (transaction.Status == TransactionStatus.RefundFailed)
        {
            throw new BadRequestException(root.GetProperty("message").GetString());
        }
    }
    public async Task<Tuple<long, List<RevenueViewModel>>> GetRevenue(RevenueRequestViewModel revenueRequest, string userId, string role)
    {
        DateTime startDate = DateTime.Parse(revenueRequest.StartDate);
        DateTime endDate = DateTime.Parse(revenueRequest.EndDate);

        IQueryable<Transaction> query = dbContext.Transactions.Include(x => x.Machine);
        if (role == "Manufacturer")
        {
            if (!string.IsNullOrEmpty(revenueRequest.ResellerId))
            {
                query = query.Where(t => t.Machine.ResellerId == revenueRequest.ResellerId);
            }
            if (!string.IsNullOrEmpty(revenueRequest.ClientId))
            {
                query = query.Where(t => t.Machine.ClientId == revenueRequest.ClientId);
            }
        }
        else if (role == "Reseller")
        {
            query = query.Where(t => t.Machine.ResellerId == userId);

            if (!string.IsNullOrEmpty(revenueRequest.ClientId))
            {
                query = query.Where(t => t.Machine.ClientId == revenueRequest.ClientId);
            }
        }
        else if (role == "Client")
        {
            query = query.Where(t => t.Machine.ClientId == userId);
        }
        query = query.Where(t => t.PaymentMode == revenueRequest.PaymentType.ToString());
        query = query.Where(t => t.CreatedTime >= startDate && t.CreatedTime <= endDate);
        List<IGrouping<string, Transaction>> groupedQuery;

        switch (revenueRequest.Granularity)
        {
            case "Daily":
                groupedQuery = query
                    .GroupBy(t => t.CreatedTime.Date.ToString("yyyy-MM-dd"))
                    .ToList();
                break;

            case "Weekly":
                groupedQuery = query
                      .GroupBy(t => StartOfWeek(t.CreatedTime).ToString("yyyy-MM-dd"))
                      .ToList();
                break;

            case "Monthly":
                groupedQuery = query
                    .GroupBy(t => t.CreatedTime.ToString("yyyy-MM"))
                    .ToList();
                break;

            case "Yearly":
                groupedQuery = query
                    .GroupBy(t => t.CreatedTime.ToString("yyyy"))
                    .ToList();
                break;

            default:
                throw new ArgumentException("Invalid granularity");
        }
        var revenueResults = groupedQuery
            .Select(g => new RevenueViewModel
            {
                StartDate = g.Key,
                EndDate = g.Key,
                Revenue = g.Sum(t => t.AmountPaid)
            })
            .ToList();
        return new Tuple<long, List<RevenueViewModel>>(revenueResults.Count, revenueResults);
    }


    private DateTime StartOfWeek(DateTime dateTime)
    {
        int diff = dateTime.DayOfWeek - DayOfWeek.Monday;
        if (diff < 0) diff += 7;
        return dateTime.AddDays(-diff).Date;
    }
    public async Task<RevenueSummaryViewModel> GetRevenueSummary(RevenueSummaryRequestViewModel revenueRequest, string userId, string role)
    {
        IQueryable<Transaction> transactionsQuery = dbContext.Transactions.Include(t => t.Machine);
        IQueryable<Machine> machinesQuery = dbContext.Machines.AsQueryable();
        if (role == "Manufacturer")
        {
            if (!string.IsNullOrEmpty(revenueRequest.ResellerId))
            {

                transactionsQuery = transactionsQuery.Where(t => t.Machine.ResellerId == revenueRequest.ResellerId);
                machinesQuery = machinesQuery.Where(m => m.ResellerId == revenueRequest.ResellerId);
            }
            if (!string.IsNullOrEmpty(revenueRequest.ClientId))
            {

                transactionsQuery = transactionsQuery.Where(t => t.Machine.ClientId == revenueRequest.ClientId);
                machinesQuery = machinesQuery.Where(m => m.ClientId == revenueRequest.ClientId);
            }
        }
        else if (role == "Reseller")
        {
            transactionsQuery = transactionsQuery.Where(t => t.Machine.ResellerId == userId);
            machinesQuery = machinesQuery.Where(m => m.ResellerId == userId);

            if (!string.IsNullOrEmpty(revenueRequest.ClientId))
            {
                transactionsQuery = transactionsQuery.Where(t => t.Machine.ClientId == revenueRequest.ClientId);
                machinesQuery = machinesQuery.Where(m => m.ClientId == revenueRequest.ClientId);
            }
        }
        else if (role == "Client")
        {
            transactionsQuery = transactionsQuery.Where(t => t.Machine.ClientId == userId);
            machinesQuery = machinesQuery.Where(m => m.ClientId == userId);
        }
        if (!string.IsNullOrEmpty(revenueRequest.MachineId))
        {
            transactionsQuery = transactionsQuery.Where(t => t.Machine.Id == revenueRequest.MachineId);
            machinesQuery = machinesQuery.Where(m => m.Id == revenueRequest.MachineId);
        }


        transactionsQuery = transactionsQuery.Where(t => t.PaymentMode == revenueRequest.PaymentType.ToString());

        DateTime startDate = DateTime.Parse(revenueRequest.StartDate);
        DateTime endDate = DateTime.Parse(revenueRequest.EndDate);


        transactionsQuery = transactionsQuery.Where(t => t.CreatedTime >= startDate && t.CreatedTime <= endDate);


        var totalRevenue = await transactionsQuery.SumAsync(t => t.AmountPaid);
        var totalTransactions = await transactionsQuery.CountAsync();
        var totalMachines = await machinesQuery.CountAsync();



        return new RevenueSummaryViewModel
        {
            TotalTransactions = totalTransactions,
            TotalMachines = totalMachines,
            TotalRevenue = totalRevenue
        };
    }


}