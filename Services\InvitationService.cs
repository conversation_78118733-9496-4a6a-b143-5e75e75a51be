using System.Collections;
using VendorMonitor.Api.Exceptions;
using VendorMonitor.Api.Models;
using VendorMonitor.Api.Utilities;
using VendorMonitor.Api.ViewModels;
using AutoMapper;
using Microsoft.Extensions.Options;

using VendorMonitor.Api.Data;
using Microsoft.EntityFrameworkCore;

namespace VendorMonitor.Api.Services;

public class InvitationService(ApplicationDbContext dbContext, JWTService jwtService, IMapper mapper)
{
    public async Task<List<ListInvitationViewModel>> GetInvitations(string userId)
    {
        return mapper.Map<List<ListInvitationViewModel>>(await dbContext.Invitations.Where(_ => _.CreatedBy == userId).ToListAsync());
    }

    public async Task<string> CreateInvitation(SaveInvitationViewModel createInvitationViewModel)
    {
        var code = RandomUtils.RandomAlphaNumeric(100);
        var codeExpiry = DateTime.Now.AddDays(1);
        var user = await dbContext.Users.Where(_ => _.EmailAddress == createInvitationViewModel.EmailAddress.ToLowerInvariant() && !_.IsDeleted).FirstOrDefaultAsync();
        if (user != null) {
            throw new BadRequestException("User exists in the system");
        }
        var invitation = await dbContext.Invitations.Where(_ => _.EmailAddress == createInvitationViewModel.EmailAddress.ToLowerInvariant()).FirstOrDefaultAsync();
        if (invitation != null)
        {
            invitation.Code = code;
            invitation.CodeExpiry = codeExpiry;
            await dbContext.SaveChangesAsync();
        }
        else
        {
            invitation = mapper.Map<Invitation>(createInvitationViewModel);
            invitation.Code = code;
            invitation.CodeExpiry = codeExpiry;
            await dbContext.AddAsync(invitation);
            await dbContext.SaveChangesAsync();
        }
        return code;
    }

    public async Task<LoginResponseViewModel> RedeemInvitation(RedeemInvitationViewModel redeemInvitationViewModel)
    {

        var invitation = await dbContext.Invitations.Where(_ => _.Code == redeemInvitationViewModel.Code).FirstOrDefaultAsync();
        if (invitation != null && invitation.CodeExpiry > DateTime.Now)
        {
            var user = new User()
            {
                EmailAddress = invitation.EmailAddress,
                FirstName = invitation.FirstName,
                LastName = invitation.LastName,
                Role = invitation.Role,
                IsActive = true,
                PasswordHash = EncryptionUtils.HashPassword(redeemInvitationViewModel.Password),
                OwnedBy = invitation.CreatedBy
            };
            await dbContext.AddAsync(user);
            dbContext.Invitations.Remove(invitation);
            await dbContext.SaveChangesAsync();
            var tokenExpiry = DateTime.UtcNow.AddMinutes(60);
            return new LoginResponseViewModel()
            {
                Token = jwtService.GenerateApplicationToken(user.Id, user.Role, tokenExpiry),
                Role = user.Role,
                TokenExpiresAt = tokenExpiry,
                InactivityExpiryInMinutes = 10
            };
        }
        else
        {
            throw new NotFoundException("code is invalid or expired");
        }
    }

    public async Task DeleteInvitation(string invitationId)
    {
        var invitation = await GetInvitationInternal(invitationId);
        dbContext.Invitations.Remove(invitation);
        await dbContext.SaveChangesAsync();
    }

    internal async Task<Invitation> GetInvitationInternal(string invitationId)
    {
        var invitation = await dbContext.Invitations.Where(_ => _.Id == invitationId).FirstOrDefaultAsync();
        if (invitation == null)
        {
            throw new NotFoundException("Invitation not found for this application");
        }
        return invitation;
    }
}