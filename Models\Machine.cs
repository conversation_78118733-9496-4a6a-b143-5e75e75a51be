namespace VendorMonitor.Api.Models;


public class Machine : AuditableEntity
{
    public string Name { get; set; }
    public string MachineId { get; set; }
    public string ProductId { get; set; }
    public string ResellerId { get; set; }
    public string ClientId { get; set; }
    public string Location { get; set; }
    public decimal Price { get; set; }
    public string QrCodeUrl { get; set; }
    public int AvailableStock { get; set; }
    public int TotalStockCapacity { get; set; }
    public string MaintainerEmail { get; set; }
    public string MaintainerNumber { get; set; }
    public DateTime LastHeartBeatTime { get; set; }
    public string Status { get; set; }
    public string VirtualAccountId { get; set; }

    //BookKeeping
    public int QRDispensed { get; set; }
    public int CoinDispensed { get; set; } 
    public string ActiveTransaction { get; set; }
}