using System.Collections;
using System.Security.Claims;
using System.Text.Json.Nodes;
using VendorMonitor.Api.Infrastructure;
using VendorMonitor.Api.Services;
using VendorMonitor.Api.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using VendorMonitor.Api.ViewModels;
using Microsoft.Extensions.Options;
using VendorMonitor.Api.Common;
using FluentEmail.Core;

namespace VendorMonitor.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class AccountController(UserService userService, JWTService jwtService, EmailService emailService, IOptions<GeneralSettings> generalSettings) : ControllerBase
{
    [HttpPost]
    [AllowAnonymous]
    [Route("{emailAddress}/token")]
    public async Task<IActionResult> LoginUser(string emailAddress, [FromBody] LoginUserViewModel loginUserViewModel)
    {
        var userTuple = await userService.LoginUser(emailAddress, loginUserViewModel);
        return new JsonResult(new
        {
        Result = GenerateLoginResponse(userTuple.Item1, userTuple.Item2)
        });
    }

    [HttpPut]
    [AllowAnonymous]
    [Route("{username}/password")]
    public async Task<IActionResult> ChangePassword(string username, [FromBody] ChangePasswordViewModel changePasswordViewModel)
    {
        return new JsonResult(new
        {
            Result = await userService.ChangePassword(username, changePasswordViewModel)
        });
    }

    [HttpGet]
    [Authorize]
    [Route("me/profile")]
    public async Task<IActionResult> GetProfile()
    {
        var userId = HttpContext.User.Claims.FirstOrDefault(claim => claim.Type == ClaimTypes.NameIdentifier)?.Value;

        return new JsonResult(new
        {
            Success = true,
            Response = await userService.GetProfile(userId)
        });
    }

    [HttpGet]
    [Authorize]
    [Route("me/avatar")]
    public async Task<IActionResult> GetAvatar()
    {
        var userId = HttpContext.User.Claims.FirstOrDefault(claim => claim.Type == ClaimTypes.NameIdentifier)?.Value;

        return new JsonResult(new
        {
            Success = true,
            Response = await userService.GetProfile(userId)
        });
    }


    [HttpPut]
    [Authorize]
    [Route("me/profile")]
    public async Task<IActionResult> UpdateProfile(SaveUserViewModel saveUserViewModel)
    {
        var userId = HttpContext.User.Claims.FirstOrDefault(claim => claim.Type == ClaimTypes.NameIdentifier)?.Value;
        await userService.UpdateProfile(userId, saveUserViewModel);
        return Ok(new { });
    }

    [HttpDelete]
    [AllowAnonymous]
    [Route("{username}/password")]
    public async Task<IActionResult> InitiatePasswordReset(string username)
    {
        var otp = await userService.InitiatePasswordReset(username);
        if (!String.IsNullOrEmpty(otp)) {
            await emailService.SendResetPasswordOtp(username, otp);
        }
        return Ok(new
        {
        });
    }

    [HttpPost]
    [Route("me/sub-merchant")]
    [Authorize]
    public async Task<IActionResult> CreateSubMerchantInEaseBuzz(SaveMerchantViewModel saveMerchantViewModel)
    {
        var userId = HttpContext.User.Claims.FirstOrDefault(claim => claim.Type == ClaimTypes.NameIdentifier)?.Value;
        await userService.CreateSubMerchantInEaseBuzz(userId, saveMerchantViewModel);
        return Ok(new { });
    }
    private LoginResponseViewModel GenerateLoginResponse(string userId, string role)
    {
        var tokenExpiry = DateTime.UtcNow.AddMinutes(60);

        return new LoginResponseViewModel()
        {
            Token = jwtService.GenerateApplicationToken(userId, role, tokenExpiry),
            ReportsToken =jwtService.GenerateReportsToken(userId, role, tokenExpiry),
            Role = role,
            TokenExpiresAt = tokenExpiry,
            InactivityExpiryInMinutes = 10
        };
    }
  


}

