using System.Collections;
using System.Text.Json.Nodes;
using VendorMonitor.Api.Common;
using VendorMonitor.Api.Infrastructure;
using VendorMonitor.Api.Services;
using VendorMonitor.Api.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System.Security.Claims;

namespace VendorMonitor.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class InvitationController(InvitationService invitationService, EmailService emailService, IOptions<GeneralSettings> generalSettings) : ControllerBase
{
    private readonly GeneralSettings _generalSettings = generalSettings.Value;

    [HttpGet]
    [Authorize(Policy = "ManufacturerOrReseller")]
    public async Task<IActionResult> GetInvitations()
    {
        var userId = HttpContext.User.Claims.FirstOrDefault(claim => claim.Type == ClaimTypes.NameIdentifier)?.Value;
        return new JsonResult(new
        {
            Status = 200,
            Result = await invitationService.GetInvitations(userId)
        });
    }

    [HttpPost]
    [Authorize(Policy = "ManufacturerOrReseller")]
    public async Task<IActionResult> CreateInvitation(SaveInvitationViewModel createInvitationViewModel)
    {
        await CreateInvitationAndSendInviteEmail(createInvitationViewModel);
        return new JsonResult(new
        {

        });
    }

    [HttpPost]
    [Route("redeem")]
    [AllowAnonymous]
    public async Task<IActionResult> RedeemInvitation(RedeemInvitationViewModel redeemInvitationViewModel)
    {
        return new JsonResult(new
        {
            Status = 200,
            Result = await invitationService.RedeemInvitation(redeemInvitationViewModel)
        });
    }


    private async Task CreateInvitationAndSendInviteEmail(SaveInvitationViewModel createInvitationViewModel)
    {
        var invitationCode = await invitationService.CreateInvitation(createInvitationViewModel);
        await emailService.SendInvitation(createInvitationViewModel.EmailAddress, $"{generalSettings.Value.BaseUrl}/redeem?token={invitationCode}");

    }

    [HttpDelete]
    [Route("{id}")]
    [Authorize(Policy = "Manufacturer")]
    public async Task<IActionResult> DeleteInvitation(string id)
    {
        await invitationService.DeleteInvitation(id);
        return Ok(new { });
    }
}
