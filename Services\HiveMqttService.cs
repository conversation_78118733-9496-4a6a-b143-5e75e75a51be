using System.Net.Http.Headers;
using HiveMQtt.Client;
using HiveMQtt.Client.Options;
using HiveMQtt.MQTT5.Types;
using Microsoft.Extensions.Options;
using MQTTnet;
using VendorMonitor.Api.Common;

namespace VendorMonitor.Api.Services;

public class HiveMQTTService : IDisposable
{
    private readonly Lazy<Task<HiveMQClient>> _connectedClient;
    private  ILogger<HiveMQTTService> _logger;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly Dictionary<string, List<string>> _subscriptions = new Dictionary<string, List<string>>();
    private readonly MQTTSettings _mqttSettings;
    public HiveMQTTService(IHttpClientFactory httpClientFactory, IOptions<MQTTSettings> mqttSettingsAccessor,ILogger<HiveMQTTService> logger)
    {

        _httpClientFactory = httpClientFactory;
        _mqttSettings = mqttSettingsAccessor.Value;
        _logger = logger;
                        _logger.LogInformation("$===============Hive start");
        // var provider = new CertificateProvider();
        var options = new HiveMQClientOptions
        {
            Host = _mqttSettings.Host,
            Port = _mqttSettings.Port,
            UseTLS = false
        };
      _logger.LogInformation("$=============== {Host}", _mqttSettings.Host);
            _logger.LogInformation("$=============== {Topic}", mqttSettingsAccessor.Value);
        this._connectedClient = new Lazy<Task<HiveMQClient>>(async () =>
        {
            var client = new HiveMQClient(options);
            client.OnMessageReceived += async (sender, args) =>
            {
                if (_subscriptions.ContainsKey(args.PublishMessage.Topic))
                {
                    var payload = args.PublishMessage.PayloadAsString;
                    using (var client = _httpClientFactory.CreateClient())
                    {
                        foreach (var url in _subscriptions[args.PublishMessage.Topic])
                        {
                            var byteContent = new ByteArrayContent(System.Text.Encoding.UTF8.GetBytes(payload));
                            byteContent.Headers.ContentType = new MediaTypeHeaderValue("application/json");

                            var response = await client.PostAsync(url, byteContent);
                            var responseContent = await response.Content.ReadAsStringAsync();
                            Console.WriteLine(responseContent);
                        }
                    }
                }
            };
        _logger.LogInformation("$==============test");
            var connectResult = await client.ConnectAsync().ConfigureAwait(false);
        _logger.LogInformation("$===============ReceiveData1");
            foreach (var subscription in _mqttSettings.Subscriptions.Keys)
            {
                  _logger.LogInformation("$===============ReceiveData2");
                await ReceiveData(client, subscription, _mqttSettings.Subscriptions[subscription]);
            }
            return client;
        });
       // var client = this._connectedClient.Value.Result;
       // _mqttSettings = mqttSettingsAccessor.Value;
    }

    public void Dummy() 
    {}

    public async Task SendData(string topic, string payload)
    {
        var client = await this._connectedClient.Value;
        var message = new MqttApplicationMessageBuilder()
                .WithTopic(topic)
                .WithPayload(payload)
                .Build();
        int retryCount = 0;
        while (retryCount < 3)
        {
            try
            {
                await client.PublishAsync(topic, payload, QualityOfService.AtMostOnceDelivery).ConfigureAwait(false);
                break;
            }
            catch (Exception ex)
            {
                Thread.Sleep(1000);
                retryCount++;
            }
        }
    }

    private async Task ReceiveData(HiveMQClient client, string topic, string webhookUrl)
    {

        await client.SubscribeAsync(topic).ConfigureAwait(false);
        if (!_subscriptions.ContainsKey(topic))
        {
            _subscriptions.Add(topic, new List<string>());
        }
        _subscriptions[topic].Add(webhookUrl);
    }



    public void Dispose()
    {
        if (this._connectedClient.IsValueCreated)
            this._connectedClient.Value.Dispose();
    }

}