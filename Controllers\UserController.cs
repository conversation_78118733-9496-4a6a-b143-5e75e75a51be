using System.Collections;
using System.Security.Claims;
using System.Text.Json.Nodes;
using VendorMonitor.Api.Infrastructure;
using VendorMonitor.Api.Services;
using VendorMonitor.Api.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using VendorMonitor.Api.Exceptions;

namespace VendorMonitor.Api.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class UserController(UserService userService) : ControllerBase
    {
        [HttpGet]
        [Authorize]
        public async Task<IActionResult> GetUsers(int page = 0, int size = 20, string role = null)
        {
            var userId = HttpContext.User.Claims.FirstOrDefault(claim => claim.Type == ClaimTypes.NameIdentifier)?.Value;
            var result = await userService.GetUsers(page, size > 20 ? 20 : size, role, userId);
            return new JsonResult(new
            {
                Count = result.Item1,
                Result = result.Item2
            });
        }

        [HttpGet]
        [Authorize]
        [Route("idnames")]
        public async Task<IActionResult> GetIdNames(string role, string userId)
        {
            var currentRole = HttpContext.User.Claims.FirstOrDefault(claim => claim.Type == ClaimTypes.Role)?.Value;
            var currentUserId = HttpContext.User.Claims.FirstOrDefault(claim => claim.Type == ClaimTypes.NameIdentifier)?.Value;
            if (currentRole == "Manufacturer") {
                return new JsonResult(new
                {
                    Result = await userService.GetIdNames(userId, role)
                });
            } else if (currentRole == "Reseller") {
                return new JsonResult(new
                {
                    Result = await userService.GetIdNames(currentUserId, "Client")
                });
            } else {
                throw new BadRequestException("You are not allowed to access IdNames");
            }
           
        }

        [HttpDelete("{userId}")]
        [Authorize(Policy = "Manufacturer")]
        public async Task<IActionResult> DeleteUser(string userId)
        {
            await userService.DeleteUser(userId);
            return Ok(new { });
        }
    }

}