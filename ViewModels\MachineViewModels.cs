using System.ComponentModel.DataAnnotations;
namespace VendorMonitor.Api.ViewModels;

public class SaveMachineViewModel
{
    public string ClientId { get; set; }
    public string Location { get; set; }
    public decimal Price { get; set; }
    public int AvailableStock { get; set; }
    public int TotalStockCapacity { get; set; }
    public string MaintainerEmail { get; set; }
    public string MaintainerNumber { get; set; }

}
public class CreateMachineViewModel
{
    [Required]
    public int Quantity { get; set; }
    public string ResellerId { get; set; }
}

public class AssignMachineViewModel
{
    [Required]
    public string ProductId { get; set; }
    [Required]
    public string ClientId { get; set; }
    [Required]
    public string MachineId { get; set; }
    public decimal? Price { get; set; }
    public int? TotalStockCapacity { get; set; }
}

public class ResellerUpdateViewModel
{
    public decimal? Price { get; set; }
    public int? TotalStockCapacity { get; set; }
}

public class ClientUpdateViewModel
{
    public string Location { get; set; }
    public string MaintainerEmail { get; set; }
    public string MaintainerNumber { get; set; }
}

public class ListMachineViewModel
{
    public string Id { get; set; }
    public string Name { get; set; }
    public string MachineId { get; set; }
    public string ClientId { get; set; }
    public string ResellerId { get; set; }
    public string Location { get; set; }
    public decimal Price { get; set; }
    public string QrCodeUrl { get; set; }
    public int AvailableStock { get; set; }
    public int TotalStockCapacity { get; set; }
    public string MaintainerEmail { get; set; }
    public string MaintainerNumber { get; set; }
    public DateTime LastHeartBeatTime { get; set; }
    public string Status { get; set; }
}

public class ListTransactionViewModel
{
    public string Id { get; set; }
    public string MachineId { get; set; }
    public string FailureReason { get; set; }
    public string Reference { get; set; }
    public string EasebuzzTransactionId { get; set; }
    public decimal AmountPaid { get; set; }
    public string PaymentMode { get; set; }
    public string Mode { get; set; }
    public string RemitterName { get; set; }
    public string RemitterPhoneNumber { get; set; }
    public string RemitterAccountNumber { get; set; }
    public string RemitterIFSC { get; set; }
    public string RemitterUpiHandle { get; set; }
    public string EaseBuzzStatus { get; set; }
    public string Status { get; set; }
    public DateTime CreatedTime { get; set; }
}
