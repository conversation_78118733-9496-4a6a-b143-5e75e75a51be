using VendorMonitor.Api.Exceptions;
using VendorMonitor.Api.Models;
using VendorMonitor.Api.Utilities;
using VendorMonitor.Api.ViewModels;
using AutoMapper;
using VendorMonitor.Api.Data;
using System.Text;
using System.Text.Json;
using System.Security.Cryptography;
using Microsoft.Extensions.Options;
using Microsoft.EntityFrameworkCore;
using VendorMonitor.Api.Common;
using VendorMonitor.Api.ViewModels;


namespace VendorMonitor.Api.Services;
public class UserService(ApplicationDbContext dbContext, JWTService jwtService, EmailService emailService, IOptions<JWTSettings> jwtSettings, IMapper mapper)
{
    private JWTSettings _jwtSettings => jwtSettings.Value;

    public async Task<Tuple<string, string>> LoginUser(string emailAddress, LoginUserViewModel loginUserViewModel)
    {
        var user = await dbContext.Users.Where(_ => _.EmailAddress == emailAddress.ToLowerInvariant() && _.IsDeleted == false)
            .FirstOrDefaultAsync();
        if (user == null)
        {
            throw new UnauthorizedException("Either the username or password is invalid");
        }
        if (EncryptionUtils.VerifyPassword(loginUserViewModel.Password, user.PasswordHash))
        {
            return new Tuple<string, string>(user.Id, user.Role);
        }
        else
        {
            throw new UnauthorizedException("Either the username or password is invalid");
        }
    }

    public async Task<List<IdNameViewModel>> GetIdNames(string userId, string role)
    {
        switch (role)
        {
            case "Reseller":
                return mapper.Map<List<IdNameViewModel>>(await dbContext.Users.Where(u => !u.IsDeleted && u.Role == role).ToListAsync());
            case "Client":
                return mapper.Map<List<IdNameViewModel>>(await dbContext.Users.Where(u => !u.IsDeleted && u.OwnedBy == userId && u.Role == role).ToListAsync());
            default:
                throw new BadRequestException("Role not supported");
        }

    }

    public async Task<Tuple<long, List<ListUserViewModel>>> GetUsers(int page, int size, string role, string userId)
    {
        var query = dbContext.Users.Where(u => !u.IsDeleted);

        if (!string.IsNullOrEmpty(role))
        {
            query = query.Where(u => u.Role == role);
        }
        var count = await query.Where(x => x.OwnedBy == userId).CountAsync();
        var result = await query.Where(x => x.OwnedBy == userId).Skip(page * size).Take(size).ToListAsync();
        return new Tuple<long, List<ListUserViewModel>>(count, mapper.Map<List<ListUserViewModel>>(result));
    }

    public async Task DeleteUser(string userId)
    {
        var user = await GetUserInternal(userId);
        user.IsDeleted = true;
        await dbContext.SaveChangesAsync();

    }

    public async Task<string> InitiatePasswordReset(string emailAddres)
    {
        var user = await dbContext.Users.Where(user => user.EmailAddress == emailAddres.ToLowerInvariant()).FirstOrDefaultAsync();
        if (user != null)
        {
            user.OTP = RandomUtils.RandomNumber(6);
            user.OTPExpiry = DateTime.Now.AddMinutes(2);
            await dbContext.SaveChangesAsync();
            return user.OTP;
        }
        return String.Empty;
    }

    public async Task<LoginResponseViewModel> ChangePassword(string emailAddress, ChangePasswordViewModel changePasswordViewModel)
    {
        var user = await dbContext.Users.Where(_ => _.EmailAddress == emailAddress.ToLowerInvariant() && _.IsDeleted == false).FirstOrDefaultAsync();
        if (user != null)
        {
            switch (changePasswordViewModel.ProofType)
            {
                case ProofTypes.Password:
                    if (EncryptionUtils.VerifyPassword(changePasswordViewModel.Proof, user.PasswordHash))
                    {
                        user.PasswordHash = EncryptionUtils.HashPassword(changePasswordViewModel.NewPassword);
                        await dbContext.SaveChangesAsync();
                        var tokenExpiry = DateTime.UtcNow.AddMinutes(60);
                        return new LoginResponseViewModel()
                        {
                            Token = jwtService.GenerateApplicationToken(user.Id, user.Role, tokenExpiry),
                            Role = user.Role,
                            TokenExpiresAt = tokenExpiry,
                            InactivityExpiryInMinutes = 10
                        };
                    }
                    else
                    {
                        throw new UnauthorizedException("Provided Password is invalid");
                    }
                case ProofTypes.OTP:
                    if (user.OTP.Equals(changePasswordViewModel.Proof) && user.OTPExpiry > DateTime.Now)
                    {
                        user.PasswordHash = EncryptionUtils.HashPassword(changePasswordViewModel.NewPassword);
                        user.OTP = null;
                        await dbContext.SaveChangesAsync();
                        var tokenExpiry = DateTime.UtcNow.AddMinutes(60);
                        return new LoginResponseViewModel()
                        {
                            Token = jwtService.GenerateApplicationToken(user.Id, user.Role, tokenExpiry),
                            Role = user.Role,
                            TokenExpiresAt = tokenExpiry,
                            InactivityExpiryInMinutes = 10
                        };

                    }
                    else
                    {
                        throw new UnauthorizedException("OTP is invalid or expired");
                    }
                default:
                    throw new UnauthorizedException("Not a valid proof type");
            }
        }
        else
        {
            throw new NotFoundException("User not found");
        }

    }

    public async Task<ViewUserViewModel> GetProfile(string userId)
    {
        var user = await GetUserInternal(userId);
        return mapper.Map<ViewUserViewModel>(user);
    }
    public async Task UpdateProfile(string userId, SaveUserViewModel saveUserViewModel)
    {
        var user = await GetUserInternal(userId);
        // mapper.Map(saveUserViewModel, user);
        user.FirstName =saveUserViewModel.FirstName;
        user.LastName = saveUserViewModel.LastName;
        user.PhoneNumber = saveUserViewModel.PhoneNumber;
          await dbContext.SaveChangesAsync();
        if (user.Role == "Client" && !String.IsNullOrWhiteSpace(saveUserViewModel.Key))
        {
            try
            {
                var merchant = await GetUserInternalByRole("Manufacturer");
                await UpdateWebHookUrl(merchant.Key, merchant.Salt, saveUserViewModel.Key);
                user.Key = saveUserViewModel.Key;
                user.Salt = saveUserViewModel.Salt; 
                await dbContext.SaveChangesAsync();
            }
            catch (NotFoundException ex)
            {

            }
        }
    }
private async Task UpdateWebHookUrl(string merchantKey, string merchantSalt, string submerchantKey)
{
    string webhookUrl = "https://vendormonitor.invimatic.com/api/Easebuzz/callback";

    var requestBody = new
    {
        key = merchantKey,
        merchant_key = submerchantKey,
        webhook_conf = new[]
        {
            new
            {
                event_type = "TRANSACTION_CREDIT",
                status = "enable",
                url = webhookUrl
            }
        }
    };

    var jsonContent = new StringContent(JsonSerializer.Serialize(requestBody), Encoding.UTF8, "application/json");

    var hashInput = $"{merchantKey}|{merchantSalt}";
    var hashedValue = GenerateHash(hashInput);

    using var httpClient = new HttpClient();
    httpClient.DefaultRequestHeaders.Add("Authorization", hashedValue);
    httpClient.DefaultRequestHeaders.Add("WIRE-API-KEY", merchantKey);

    var response = await httpClient.PutAsync($"https://wire.easebuzz.in/api/v1/insta-collect/merchants/webhooks/", jsonContent);
    var responseContent = await response.Content.ReadAsStringAsync();

  using var doc = JsonDocument.Parse(responseContent);
    var root = doc.RootElement;
    if (!response.IsSuccessStatusCode || root.TryGetProperty("success", out var successElement) && !successElement.GetBoolean())
    {
        throw new BadRequestException($"Request failed: {responseContent}");
    }
}
internal async Task<User> GetUserInternal(string userId)
{
    var user = await dbContext.Users.Where(_ => _.Id == userId).FirstOrDefaultAsync();
    if (user == null)
    {
        throw new NotFoundException("user not found");
    }
    return user;
}
internal async Task<User> GetUserInternalByRole(string roleName)
{
    var user = await dbContext.Users
        .Where(x => x.Role == roleName && x.Key != null && x.Salt != null)
        .FirstOrDefaultAsync();

    if (user == null)
    {
        throw new NotFoundException("User not found");
    }

    return user;
}

public async Task<string> CreateSubMerchantInEaseBuzz(string userId, SaveMerchantViewModel saveMerchantViewModel)
{
    var maintainer = await dbContext.Users
                                    .Where(x => x.Role == "Manufacturer")
                                    .Select(x => new { x.Key, x.Salt })
                                    .FirstOrDefaultAsync();

    if (maintainer == null)
    {
        throw new NotFoundException("Maintainer not found.");
    }

    var requestBody = new
    {
        key = maintainer.Key,
        name = saveMerchantViewModel.NameOnBank,
        name_on_bank = saveMerchantViewModel.NameOnBank,
        email = saveMerchantViewModel.Email,
        phone = saveMerchantViewModel.Phone,
        business_name = saveMerchantViewModel.BusinessName,
        bank_name = saveMerchantViewModel.BankName,
        account_number = saveMerchantViewModel.AccountNumber,
        account_ifsc = saveMerchantViewModel.AccountIFSC,
        bank_branch = saveMerchantViewModel.BankBranch,
        entity_type = saveMerchantViewModel.EntityType
    };

    var jsonContent = new StringContent(JsonSerializer.Serialize(requestBody), Encoding.UTF8, "application/json");
    var hashInput = $"{maintainer.Key}|{saveMerchantViewModel.NameOnBank}|{saveMerchantViewModel.Email}|{saveMerchantViewModel.Phone}|{maintainer.Salt}";
    var hashedValue = GenerateHash(hashInput);

    using var httpClient = new HttpClient();
    httpClient.DefaultRequestHeaders.Add("Authorization", hashedValue);
    httpClient.DefaultRequestHeaders.Add("WIRE-API-KEY", maintainer.Key);

    var response = await httpClient.PostAsync("https://wire.easebuzz.in/api/v1/merchants/create/", jsonContent);
    var responseContent = await response.Content.ReadAsStringAsync();

    if (!response.IsSuccessStatusCode)
    {
        throw new BadRequestException($"Request failed: {responseContent}");
    }
    var user = await dbContext.Users.Where(x => x.Id == userId).FirstOrDefaultAsync();
    user.SubMerchantId = ParseSubmerchantId(responseContent);
    await dbContext.SaveChangesAsync();

    return user.SubMerchantId;



}

private static string ParseSubmerchantId(string responseContent)
{
    using var doc = JsonDocument.Parse(responseContent);
    var root = doc.RootElement;

    if (root.TryGetProperty("success", out var successElement) && successElement.GetBoolean())
    {
        return root.GetProperty("submerchant_id")
                   .GetString();
    }
    throw new BadRequestException(root.GetProperty("message").GetString());
}
private string GenerateHash(string hashInput)
{
    return BitConverter.ToString(ComputeHash512(System.Text.Encoding.UTF8.GetBytes(hashInput))).Replace("-", "");
}

public byte[] ComputeHash512(byte[] payload)
{
    SHA512Managed hashString = new SHA512Managed();
    return hashString.ComputeHash(payload);
}
//     private async Task GetSubMerchant(string merchantKey, string merchantSalt, string email)
// {
//     var hashInput = $"{merchantKey}|{merchantSalt}";
//     var hashedValue = GenerateHash(hashInput);

//     using var httpClient = new HttpClient();
//     httpClient.DefaultRequestHeaders.Add("Authorization", hashedValue);
//     httpClient.DefaultRequestHeaders.Add("WIRE-API-KEY", merchantKey);
    
//     var response = await httpClient.GetAsync($"https://wire.easebuzz.in/api/v1/merchants/retrieve/?key=ECC0F35513&email={email}");
//     var responseContent = await response.Content.ReadAsStringAsync();

//     if (!response.IsSuccessStatusCode)
//     {
//         throw new BadRequestException($"Request failed: {responseContent}");
//     }

//     using var doc = JsonDocument.Parse(responseContent);
//     var root = doc.RootElement;

//     if (root.TryGetProperty("success", out var successElement) && successElement.GetBoolean())
//     {
//         var submerchantId = root.GetProperty("data").GetProperty("merchant").GetProperty("id").GetUInt32();
//         await UpdateWebHookUrl(merchantKey, merchantSalt, submerchantId.ToString());
//     }

//     throw new BadRequestException(root.GetProperty("message").GetString());
// }

// private async Task UpdateWebHookUrl(string merchantKey, string merchantSalt, string submerchantId)
// {
//     string webhookUrl = "";
//     var requestBody = new
//     {
//         key = merchantKey,
//         webhook_url = webhookUrl
//     };

//     var jsonContent = new StringContent(JsonSerializer.Serialize(requestBody), Encoding.UTF8, "application/json");
//     var hashInput = $"{merchantKey}||||{merchantSalt}";
//     var hashedValue = GenerateHash(hashInput);

//     using var httpClient = new HttpClient();
//     httpClient.DefaultRequestHeaders.Add("Authorization", hashedValue);
//     httpClient.DefaultRequestHeaders.Add("WIRE-API-KEY", merchantKey);

//     var response = await httpClient.PutAsync($"https://wire.easebuzz.in/api/v1/merchants/{submerchantId}/", jsonContent);
//     var responseContent = await response.Content.ReadAsStringAsync();

//     if (!response.IsSuccessStatusCode)
//     {
//         throw new BadRequestException($"Request failed: {responseContent}");
//     }
// }
}
