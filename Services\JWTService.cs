using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using VendorMonitor.Api.Exceptions;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using VendorMonitor.Api.Common;

namespace VendorMonitor.Api.Services;

public class JWTService(IOptions<JWTSettings> jwtSettings)
{
    private readonly JWTSettings _jwtSettings = jwtSettings.Value;

    public string GenerateApplicationToken(string userId, string role, DateTime tokenExpiry)
    {
        var claims = new[] {
                new Claim(ClaimTypes.NameIdentifier, userId),
                new Claim(ClaimTypes.Role, role)
            };
        return GenerateToken(claims, tokenExpiry);
    }

    public string GenerateToken(Claim[] claims, DateTime tokenExpiry)
    {
        var securityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtSettings.Key));
        var credentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256Signature);
        var tokenDescriptor = new JwtSecurityToken(issuer: _jwtSettings.Issuer, claims: claims,
            expires: tokenExpiry, signingCredentials: credentials);
        return new JwtSecurityTokenHandler().WriteToken(tokenDescriptor);
    }

    public JwtSecurityToken ValidateToken(string token)
    {
        try
        {
            var handler = new JwtSecurityTokenHandler();
            var jwt = handler.ReadJwtToken(token);
            var validationParameters = new TokenValidationParameters
            {
                ValidateIssuer = true,
                ValidateLifetime = true,
                ClockSkew = TimeSpan.Zero,
                ValidateAudience = false,
                ValidateIssuerSigningKey = true,
                ValidIssuer = _jwtSettings.Issuer,
                IssuerSigningKey = new
                SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtSettings.Key))
            };
            SecurityToken validatedToken;
            handler.ValidateToken(token, validationParameters, out validatedToken);
            return jwt;
        }
        catch (Exception ex)
        {
            throw new UnauthorizedException(ex.Message);
        }
    }

    public string GenerateReportsToken(string userId, string role, DateTime tokenExpiry)
    {
        var claims = new[] {
                new Claim(ClaimTypes.NameIdentifier, userId),
                new Claim(ClaimTypes.Role, role)
            };
        return GenerateReportsToken(claims, tokenExpiry);
    }
    private string GenerateReportsToken(Claim[] claims, DateTime tokenExpiry)
    {
        var securityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtSettings.Key));
        var credentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256);
        var tokenHandler = new JwtSecurityTokenHandler();
        var tokenDescriptor = new SecurityTokenDescriptor()
        {
            Expires = tokenExpiry,
            IssuedAt = DateTime.UtcNow,
            SigningCredentials = credentials,
            Subject = new ClaimsIdentity(claims)
        };
        var token = tokenHandler.CreateToken(tokenDescriptor);
        return tokenHandler.WriteToken(token);
    }

}
