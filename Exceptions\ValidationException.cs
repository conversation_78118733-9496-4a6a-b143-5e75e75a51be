namespace VendorMonitor.Api.Exceptions;

public class ValidationException : Exception
{
    public string FieldName { get; }
    public string FieldValue { get; }
    public int? RowNumber { get; }

    public ValidationException(string fieldName, string message, string fieldValue = null, int? rowNumber = null) 
        : base(message)
    {
        FieldName = fieldName;
        FieldValue = fieldValue;
        RowNumber = rowNumber;
    }
}
